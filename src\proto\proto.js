/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from "protobufjs/minimal";

// Common aliases
const $Reader = $protobuf.Reader, $Writer = $protobuf.Writer, $util = $protobuf.util;

// Exported root namespace
const $root = $protobuf.roots["default"] || ($protobuf.roots["default"] = {});

export const auth = $root.auth = (() => {

    /**
     * Namespace auth.
     * @exports auth
     * @namespace
     */
    const auth = {};

    auth.LoginRequest = (function() {

        /**
         * Properties of a LoginRequest.
         * @memberof auth
         * @interface ILoginRequest
         * @property {string|null} [token] LoginRequest token
         * @property {common.DevicePlatform|null} [devicePlatform] LoginRequest devicePlatform
         */

        /**
         * Constructs a new LoginRequest.
         * @memberof auth
         * @classdesc Represents a LoginRequest.
         * @implements ILoginRequest
         * @constructor
         * @param {auth.ILoginRequest=} [properties] Properties to set
         */
        function LoginRequest(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * LoginRequest token.
         * @member {string} token
         * @memberof auth.LoginRequest
         * @instance
         */
        LoginRequest.prototype.token = "";

        /**
         * LoginRequest devicePlatform.
         * @member {common.DevicePlatform} devicePlatform
         * @memberof auth.LoginRequest
         * @instance
         */
        LoginRequest.prototype.devicePlatform = 0;

        /**
         * Creates a new LoginRequest instance using the specified properties.
         * @function create
         * @memberof auth.LoginRequest
         * @static
         * @param {auth.ILoginRequest=} [properties] Properties to set
         * @returns {auth.LoginRequest} LoginRequest instance
         */
        LoginRequest.create = function create(properties) {
            return new LoginRequest(properties);
        };

        /**
         * Encodes the specified LoginRequest message. Does not implicitly {@link auth.LoginRequest.verify|verify} messages.
         * @function encode
         * @memberof auth.LoginRequest
         * @static
         * @param {auth.ILoginRequest} message LoginRequest message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LoginRequest.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.token != null && Object.hasOwnProperty.call(message, "token"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.token);
            if (message.devicePlatform != null && Object.hasOwnProperty.call(message, "devicePlatform"))
                writer.uint32(/* id 2, wireType 0 =*/16).int32(message.devicePlatform);
            return writer;
        };

        /**
         * Encodes the specified LoginRequest message, length delimited. Does not implicitly {@link auth.LoginRequest.verify|verify} messages.
         * @function encodeDelimited
         * @memberof auth.LoginRequest
         * @static
         * @param {auth.ILoginRequest} message LoginRequest message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LoginRequest.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a LoginRequest message from the specified reader or buffer.
         * @function decode
         * @memberof auth.LoginRequest
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {auth.LoginRequest} LoginRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LoginRequest.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.auth.LoginRequest();
            while (reader.pos < end) {
                let tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.token = reader.string();
                        break;
                    }
                case 2: {
                        message.devicePlatform = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a LoginRequest message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof auth.LoginRequest
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {auth.LoginRequest} LoginRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LoginRequest.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a LoginRequest message.
         * @function verify
         * @memberof auth.LoginRequest
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        LoginRequest.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.token != null && message.hasOwnProperty("token"))
                if (!$util.isString(message.token))
                    return "token: string expected";
            if (message.devicePlatform != null && message.hasOwnProperty("devicePlatform"))
                switch (message.devicePlatform) {
                default:
                    return "devicePlatform: enum value expected";
                case 0:
                case 1:
                    break;
                }
            return null;
        };

        /**
         * Creates a LoginRequest message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof auth.LoginRequest
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {auth.LoginRequest} LoginRequest
         */
        LoginRequest.fromObject = function fromObject(object) {
            if (object instanceof $root.auth.LoginRequest)
                return object;
            let message = new $root.auth.LoginRequest();
            if (object.token != null)
                message.token = String(object.token);
            switch (object.devicePlatform) {
            default:
                if (typeof object.devicePlatform === "number") {
                    message.devicePlatform = object.devicePlatform;
                    break;
                }
                break;
            case "MOBILE":
            case 0:
                message.devicePlatform = 0;
                break;
            case "WEB":
            case 1:
                message.devicePlatform = 1;
                break;
            }
            return message;
        };

        /**
         * Creates a plain object from a LoginRequest message. Also converts values to other types if specified.
         * @function toObject
         * @memberof auth.LoginRequest
         * @static
         * @param {auth.LoginRequest} message LoginRequest
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        LoginRequest.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults) {
                object.token = "";
                object.devicePlatform = options.enums === String ? "MOBILE" : 0;
            }
            if (message.token != null && message.hasOwnProperty("token"))
                object.token = message.token;
            if (message.devicePlatform != null && message.hasOwnProperty("devicePlatform"))
                object.devicePlatform = options.enums === String ? $root.common.DevicePlatform[message.devicePlatform] === undefined ? message.devicePlatform : $root.common.DevicePlatform[message.devicePlatform] : message.devicePlatform;
            return object;
        };

        /**
         * Converts this LoginRequest to JSON.
         * @function toJSON
         * @memberof auth.LoginRequest
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        LoginRequest.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for LoginRequest
         * @function getTypeUrl
         * @memberof auth.LoginRequest
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        LoginRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/auth.LoginRequest";
        };

        return LoginRequest;
    })();

    auth.LoginResponse = (function() {

        /**
         * Properties of a LoginResponse.
         * @memberof auth
         * @interface ILoginResponse
         * @property {boolean|null} [success] LoginResponse success
         * @property {string|null} [errorMessage] LoginResponse errorMessage
         * @property {common.IUser|null} [user] LoginResponse user
         */

        /**
         * Constructs a new LoginResponse.
         * @memberof auth
         * @classdesc Represents a LoginResponse.
         * @implements ILoginResponse
         * @constructor
         * @param {auth.ILoginResponse=} [properties] Properties to set
         */
        function LoginResponse(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * LoginResponse success.
         * @member {boolean} success
         * @memberof auth.LoginResponse
         * @instance
         */
        LoginResponse.prototype.success = false;

        /**
         * LoginResponse errorMessage.
         * @member {string} errorMessage
         * @memberof auth.LoginResponse
         * @instance
         */
        LoginResponse.prototype.errorMessage = "";

        /**
         * LoginResponse user.
         * @member {common.IUser|null|undefined} user
         * @memberof auth.LoginResponse
         * @instance
         */
        LoginResponse.prototype.user = null;

        /**
         * Creates a new LoginResponse instance using the specified properties.
         * @function create
         * @memberof auth.LoginResponse
         * @static
         * @param {auth.ILoginResponse=} [properties] Properties to set
         * @returns {auth.LoginResponse} LoginResponse instance
         */
        LoginResponse.create = function create(properties) {
            return new LoginResponse(properties);
        };

        /**
         * Encodes the specified LoginResponse message. Does not implicitly {@link auth.LoginResponse.verify|verify} messages.
         * @function encode
         * @memberof auth.LoginResponse
         * @static
         * @param {auth.ILoginResponse} message LoginResponse message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LoginResponse.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.success != null && Object.hasOwnProperty.call(message, "success"))
                writer.uint32(/* id 1, wireType 0 =*/8).bool(message.success);
            if (message.errorMessage != null && Object.hasOwnProperty.call(message, "errorMessage"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.errorMessage);
            if (message.user != null && Object.hasOwnProperty.call(message, "user"))
                $root.common.User.encode(message.user, writer.uint32(/* id 4, wireType 2 =*/34).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified LoginResponse message, length delimited. Does not implicitly {@link auth.LoginResponse.verify|verify} messages.
         * @function encodeDelimited
         * @memberof auth.LoginResponse
         * @static
         * @param {auth.ILoginResponse} message LoginResponse message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LoginResponse.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a LoginResponse message from the specified reader or buffer.
         * @function decode
         * @memberof auth.LoginResponse
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {auth.LoginResponse} LoginResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LoginResponse.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.auth.LoginResponse();
            while (reader.pos < end) {
                let tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.success = reader.bool();
                        break;
                    }
                case 3: {
                        message.errorMessage = reader.string();
                        break;
                    }
                case 4: {
                        message.user = $root.common.User.decode(reader, reader.uint32());
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a LoginResponse message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof auth.LoginResponse
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {auth.LoginResponse} LoginResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LoginResponse.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a LoginResponse message.
         * @function verify
         * @memberof auth.LoginResponse
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        LoginResponse.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.success != null && message.hasOwnProperty("success"))
                if (typeof message.success !== "boolean")
                    return "success: boolean expected";
            if (message.errorMessage != null && message.hasOwnProperty("errorMessage"))
                if (!$util.isString(message.errorMessage))
                    return "errorMessage: string expected";
            if (message.user != null && message.hasOwnProperty("user")) {
                let error = $root.common.User.verify(message.user);
                if (error)
                    return "user." + error;
            }
            return null;
        };

        /**
         * Creates a LoginResponse message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof auth.LoginResponse
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {auth.LoginResponse} LoginResponse
         */
        LoginResponse.fromObject = function fromObject(object) {
            if (object instanceof $root.auth.LoginResponse)
                return object;
            let message = new $root.auth.LoginResponse();
            if (object.success != null)
                message.success = Boolean(object.success);
            if (object.errorMessage != null)
                message.errorMessage = String(object.errorMessage);
            if (object.user != null) {
                if (typeof object.user !== "object")
                    throw TypeError(".auth.LoginResponse.user: object expected");
                message.user = $root.common.User.fromObject(object.user);
            }
            return message;
        };

        /**
         * Creates a plain object from a LoginResponse message. Also converts values to other types if specified.
         * @function toObject
         * @memberof auth.LoginResponse
         * @static
         * @param {auth.LoginResponse} message LoginResponse
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        LoginResponse.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults) {
                object.success = false;
                object.errorMessage = "";
                object.user = null;
            }
            if (message.success != null && message.hasOwnProperty("success"))
                object.success = message.success;
            if (message.errorMessage != null && message.hasOwnProperty("errorMessage"))
                object.errorMessage = message.errorMessage;
            if (message.user != null && message.hasOwnProperty("user"))
                object.user = $root.common.User.toObject(message.user, options);
            return object;
        };

        /**
         * Converts this LoginResponse to JSON.
         * @function toJSON
         * @memberof auth.LoginResponse
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        LoginResponse.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for LoginResponse
         * @function getTypeUrl
         * @memberof auth.LoginResponse
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        LoginResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/auth.LoginResponse";
        };

        return LoginResponse;
    })();

    auth.LogoutRequest = (function() {

        /**
         * Properties of a LogoutRequest.
         * @memberof auth
         * @interface ILogoutRequest
         * @property {common.DevicePlatform|null} [devicePlatform] LogoutRequest devicePlatform
         */

        /**
         * Constructs a new LogoutRequest.
         * @memberof auth
         * @classdesc Represents a LogoutRequest.
         * @implements ILogoutRequest
         * @constructor
         * @param {auth.ILogoutRequest=} [properties] Properties to set
         */
        function LogoutRequest(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * LogoutRequest devicePlatform.
         * @member {common.DevicePlatform} devicePlatform
         * @memberof auth.LogoutRequest
         * @instance
         */
        LogoutRequest.prototype.devicePlatform = 0;

        /**
         * Creates a new LogoutRequest instance using the specified properties.
         * @function create
         * @memberof auth.LogoutRequest
         * @static
         * @param {auth.ILogoutRequest=} [properties] Properties to set
         * @returns {auth.LogoutRequest} LogoutRequest instance
         */
        LogoutRequest.create = function create(properties) {
            return new LogoutRequest(properties);
        };

        /**
         * Encodes the specified LogoutRequest message. Does not implicitly {@link auth.LogoutRequest.verify|verify} messages.
         * @function encode
         * @memberof auth.LogoutRequest
         * @static
         * @param {auth.ILogoutRequest} message LogoutRequest message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LogoutRequest.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.devicePlatform != null && Object.hasOwnProperty.call(message, "devicePlatform"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.devicePlatform);
            return writer;
        };

        /**
         * Encodes the specified LogoutRequest message, length delimited. Does not implicitly {@link auth.LogoutRequest.verify|verify} messages.
         * @function encodeDelimited
         * @memberof auth.LogoutRequest
         * @static
         * @param {auth.ILogoutRequest} message LogoutRequest message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LogoutRequest.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a LogoutRequest message from the specified reader or buffer.
         * @function decode
         * @memberof auth.LogoutRequest
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {auth.LogoutRequest} LogoutRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LogoutRequest.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.auth.LogoutRequest();
            while (reader.pos < end) {
                let tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.devicePlatform = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a LogoutRequest message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof auth.LogoutRequest
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {auth.LogoutRequest} LogoutRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LogoutRequest.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a LogoutRequest message.
         * @function verify
         * @memberof auth.LogoutRequest
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        LogoutRequest.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.devicePlatform != null && message.hasOwnProperty("devicePlatform"))
                switch (message.devicePlatform) {
                default:
                    return "devicePlatform: enum value expected";
                case 0:
                case 1:
                    break;
                }
            return null;
        };

        /**
         * Creates a LogoutRequest message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof auth.LogoutRequest
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {auth.LogoutRequest} LogoutRequest
         */
        LogoutRequest.fromObject = function fromObject(object) {
            if (object instanceof $root.auth.LogoutRequest)
                return object;
            let message = new $root.auth.LogoutRequest();
            switch (object.devicePlatform) {
            default:
                if (typeof object.devicePlatform === "number") {
                    message.devicePlatform = object.devicePlatform;
                    break;
                }
                break;
            case "MOBILE":
            case 0:
                message.devicePlatform = 0;
                break;
            case "WEB":
            case 1:
                message.devicePlatform = 1;
                break;
            }
            return message;
        };

        /**
         * Creates a plain object from a LogoutRequest message. Also converts values to other types if specified.
         * @function toObject
         * @memberof auth.LogoutRequest
         * @static
         * @param {auth.LogoutRequest} message LogoutRequest
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        LogoutRequest.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults)
                object.devicePlatform = options.enums === String ? "MOBILE" : 0;
            if (message.devicePlatform != null && message.hasOwnProperty("devicePlatform"))
                object.devicePlatform = options.enums === String ? $root.common.DevicePlatform[message.devicePlatform] === undefined ? message.devicePlatform : $root.common.DevicePlatform[message.devicePlatform] : message.devicePlatform;
            return object;
        };

        /**
         * Converts this LogoutRequest to JSON.
         * @function toJSON
         * @memberof auth.LogoutRequest
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        LogoutRequest.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for LogoutRequest
         * @function getTypeUrl
         * @memberof auth.LogoutRequest
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        LogoutRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/auth.LogoutRequest";
        };

        return LogoutRequest;
    })();

    auth.LogoutResponse = (function() {

        /**
         * Properties of a LogoutResponse.
         * @memberof auth
         * @interface ILogoutResponse
         * @property {string|null} [logoutReason] LogoutResponse logoutReason
         */

        /**
         * Constructs a new LogoutResponse.
         * @memberof auth
         * @classdesc Represents a LogoutResponse.
         * @implements ILogoutResponse
         * @constructor
         * @param {auth.ILogoutResponse=} [properties] Properties to set
         */
        function LogoutResponse(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * LogoutResponse logoutReason.
         * @member {string} logoutReason
         * @memberof auth.LogoutResponse
         * @instance
         */
        LogoutResponse.prototype.logoutReason = "";

        /**
         * Creates a new LogoutResponse instance using the specified properties.
         * @function create
         * @memberof auth.LogoutResponse
         * @static
         * @param {auth.ILogoutResponse=} [properties] Properties to set
         * @returns {auth.LogoutResponse} LogoutResponse instance
         */
        LogoutResponse.create = function create(properties) {
            return new LogoutResponse(properties);
        };

        /**
         * Encodes the specified LogoutResponse message. Does not implicitly {@link auth.LogoutResponse.verify|verify} messages.
         * @function encode
         * @memberof auth.LogoutResponse
         * @static
         * @param {auth.ILogoutResponse} message LogoutResponse message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LogoutResponse.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.logoutReason != null && Object.hasOwnProperty.call(message, "logoutReason"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.logoutReason);
            return writer;
        };

        /**
         * Encodes the specified LogoutResponse message, length delimited. Does not implicitly {@link auth.LogoutResponse.verify|verify} messages.
         * @function encodeDelimited
         * @memberof auth.LogoutResponse
         * @static
         * @param {auth.ILogoutResponse} message LogoutResponse message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LogoutResponse.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a LogoutResponse message from the specified reader or buffer.
         * @function decode
         * @memberof auth.LogoutResponse
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {auth.LogoutResponse} LogoutResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LogoutResponse.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.auth.LogoutResponse();
            while (reader.pos < end) {
                let tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.logoutReason = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a LogoutResponse message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof auth.LogoutResponse
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {auth.LogoutResponse} LogoutResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LogoutResponse.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a LogoutResponse message.
         * @function verify
         * @memberof auth.LogoutResponse
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        LogoutResponse.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.logoutReason != null && message.hasOwnProperty("logoutReason"))
                if (!$util.isString(message.logoutReason))
                    return "logoutReason: string expected";
            return null;
        };

        /**
         * Creates a LogoutResponse message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof auth.LogoutResponse
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {auth.LogoutResponse} LogoutResponse
         */
        LogoutResponse.fromObject = function fromObject(object) {
            if (object instanceof $root.auth.LogoutResponse)
                return object;
            let message = new $root.auth.LogoutResponse();
            if (object.logoutReason != null)
                message.logoutReason = String(object.logoutReason);
            return message;
        };

        /**
         * Creates a plain object from a LogoutResponse message. Also converts values to other types if specified.
         * @function toObject
         * @memberof auth.LogoutResponse
         * @static
         * @param {auth.LogoutResponse} message LogoutResponse
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        LogoutResponse.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults)
                object.logoutReason = "";
            if (message.logoutReason != null && message.hasOwnProperty("logoutReason"))
                object.logoutReason = message.logoutReason;
            return object;
        };

        /**
         * Converts this LogoutResponse to JSON.
         * @function toJSON
         * @memberof auth.LogoutResponse
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        LogoutResponse.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for LogoutResponse
         * @function getTypeUrl
         * @memberof auth.LogoutResponse
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        LogoutResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/auth.LogoutResponse";
        };

        return LogoutResponse;
    })();

    return auth;
})();

export const common = $root.common = (() => {

    /**
     * Namespace common.
     * @exports common
     * @namespace
     */
    const common = {};

    /**
     * DevicePlatform enum.
     * @name common.DevicePlatform
     * @enum {number}
     * @property {number} MOBILE=0 MOBILE value
     * @property {number} WEB=1 WEB value
     */
    common.DevicePlatform = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "MOBILE"] = 0;
        values[valuesById[1] = "WEB"] = 1;
        return values;
    })();

    common.User = (function() {

        /**
         * Properties of a User.
         * @memberof common
         * @interface IUser
         * @property {string|null} [userId] User userId
         * @property {string|null} [username] User username
         * @property {string|null} [avatar] User avatar
         * @property {string|null} [level] User level
         * @property {string|null} [tag] User tag
         */

        /**
         * Constructs a new User.
         * @memberof common
         * @classdesc Represents a User.
         * @implements IUser
         * @constructor
         * @param {common.IUser=} [properties] Properties to set
         */
        function User(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * User userId.
         * @member {string} userId
         * @memberof common.User
         * @instance
         */
        User.prototype.userId = "";

        /**
         * User username.
         * @member {string} username
         * @memberof common.User
         * @instance
         */
        User.prototype.username = "";

        /**
         * User avatar.
         * @member {string} avatar
         * @memberof common.User
         * @instance
         */
        User.prototype.avatar = "";

        /**
         * User level.
         * @member {string} level
         * @memberof common.User
         * @instance
         */
        User.prototype.level = "";

        /**
         * User tag.
         * @member {string} tag
         * @memberof common.User
         * @instance
         */
        User.prototype.tag = "";

        /**
         * Creates a new User instance using the specified properties.
         * @function create
         * @memberof common.User
         * @static
         * @param {common.IUser=} [properties] Properties to set
         * @returns {common.User} User instance
         */
        User.create = function create(properties) {
            return new User(properties);
        };

        /**
         * Encodes the specified User message. Does not implicitly {@link common.User.verify|verify} messages.
         * @function encode
         * @memberof common.User
         * @static
         * @param {common.IUser} message User message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        User.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.userId != null && Object.hasOwnProperty.call(message, "userId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.userId);
            if (message.username != null && Object.hasOwnProperty.call(message, "username"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.username);
            if (message.avatar != null && Object.hasOwnProperty.call(message, "avatar"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.avatar);
            if (message.level != null && Object.hasOwnProperty.call(message, "level"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.level);
            if (message.tag != null && Object.hasOwnProperty.call(message, "tag"))
                writer.uint32(/* id 5, wireType 2 =*/42).string(message.tag);
            return writer;
        };

        /**
         * Encodes the specified User message, length delimited. Does not implicitly {@link common.User.verify|verify} messages.
         * @function encodeDelimited
         * @memberof common.User
         * @static
         * @param {common.IUser} message User message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        User.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a User message from the specified reader or buffer.
         * @function decode
         * @memberof common.User
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {common.User} User
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        User.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.common.User();
            while (reader.pos < end) {
                let tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.userId = reader.string();
                        break;
                    }
                case 2: {
                        message.username = reader.string();
                        break;
                    }
                case 3: {
                        message.avatar = reader.string();
                        break;
                    }
                case 4: {
                        message.level = reader.string();
                        break;
                    }
                case 5: {
                        message.tag = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a User message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof common.User
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {common.User} User
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        User.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a User message.
         * @function verify
         * @memberof common.User
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        User.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.userId != null && message.hasOwnProperty("userId"))
                if (!$util.isString(message.userId))
                    return "userId: string expected";
            if (message.username != null && message.hasOwnProperty("username"))
                if (!$util.isString(message.username))
                    return "username: string expected";
            if (message.avatar != null && message.hasOwnProperty("avatar"))
                if (!$util.isString(message.avatar))
                    return "avatar: string expected";
            if (message.level != null && message.hasOwnProperty("level"))
                if (!$util.isString(message.level))
                    return "level: string expected";
            if (message.tag != null && message.hasOwnProperty("tag"))
                if (!$util.isString(message.tag))
                    return "tag: string expected";
            return null;
        };

        /**
         * Creates a User message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof common.User
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {common.User} User
         */
        User.fromObject = function fromObject(object) {
            if (object instanceof $root.common.User)
                return object;
            let message = new $root.common.User();
            if (object.userId != null)
                message.userId = String(object.userId);
            if (object.username != null)
                message.username = String(object.username);
            if (object.avatar != null)
                message.avatar = String(object.avatar);
            if (object.level != null)
                message.level = String(object.level);
            if (object.tag != null)
                message.tag = String(object.tag);
            return message;
        };

        /**
         * Creates a plain object from a User message. Also converts values to other types if specified.
         * @function toObject
         * @memberof common.User
         * @static
         * @param {common.User} message User
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        User.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults) {
                object.userId = "";
                object.username = "";
                object.avatar = "";
                object.level = "";
                object.tag = "";
            }
            if (message.userId != null && message.hasOwnProperty("userId"))
                object.userId = message.userId;
            if (message.username != null && message.hasOwnProperty("username"))
                object.username = message.username;
            if (message.avatar != null && message.hasOwnProperty("avatar"))
                object.avatar = message.avatar;
            if (message.level != null && message.hasOwnProperty("level"))
                object.level = message.level;
            if (message.tag != null && message.hasOwnProperty("tag"))
                object.tag = message.tag;
            return object;
        };

        /**
         * Converts this User to JSON.
         * @function toJSON
         * @memberof common.User
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        User.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for User
         * @function getTypeUrl
         * @memberof common.User
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        User.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/common.User";
        };

        return User;
    })();

    return common;
})();

export const chat = $root.chat = (() => {

    /**
     * Namespace chat.
     * @exports chat
     * @namespace
     */
    const chat = {};

    /**
     * ChatMessageType enum.
     * @name chat.ChatMessageType
     * @enum {number}
     * @property {number} TEXT=0 TEXT value
     * @property {number} AUDIO=1 AUDIO value
     * @property {number} IMAGE=2 IMAGE value
     * @property {number} VIDEO=3 VIDEO value
     * @property {number} FILE=4 FILE value
     * @property {number} GIFT=5 GIFT value
     * @property {number} ENTER=6 ENTER value
     */
    chat.ChatMessageType = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "TEXT"] = 0;
        values[valuesById[1] = "AUDIO"] = 1;
        values[valuesById[2] = "IMAGE"] = 2;
        values[valuesById[3] = "VIDEO"] = 3;
        values[valuesById[4] = "FILE"] = 4;
        values[valuesById[5] = "GIFT"] = 5;
        values[valuesById[6] = "ENTER"] = 6;
        return values;
    })();

    /**
     * ReceiverType enum.
     * @name chat.ReceiverType
     * @enum {number}
     * @property {number} SINGLE=0 SINGLE value
     * @property {number} GROUP=1 GROUP value
     * @property {number} CHAT_ROOM=2 CHAT_ROOM value
     * @property {number} SUPER_GROUP=3 SUPER_GROUP value
     * @property {number} LIVE_ROOM=4 LIVE_ROOM value
     */
    chat.ReceiverType = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "SINGLE"] = 0;
        values[valuesById[1] = "GROUP"] = 1;
        values[valuesById[2] = "CHAT_ROOM"] = 2;
        values[valuesById[3] = "SUPER_GROUP"] = 3;
        values[valuesById[4] = "LIVE_ROOM"] = 4;
        return values;
    })();

    chat.ChatMessage = (function() {

        /**
         * Properties of a ChatMessage.
         * @memberof chat
         * @interface IChatMessage
         * @property {chat.ChatMessageType|null} [chatMessageType] ChatMessage chatMessageType
         * @property {string|null} [senderId] ChatMessage senderId
         * @property {string|null} [receiverId] ChatMessage receiverId
         * @property {string|null} [content] ChatMessage content
         * @property {number|Long|null} [sequence] ChatMessage sequence
         * @property {chat.ReceiverType|null} [receiverType] ChatMessage receiverType
         * @property {string|null} [extra] ChatMessage extra
         */

        /**
         * Constructs a new ChatMessage.
         * @memberof chat
         * @classdesc Represents a ChatMessage.
         * @implements IChatMessage
         * @constructor
         * @param {chat.IChatMessage=} [properties] Properties to set
         */
        function ChatMessage(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ChatMessage chatMessageType.
         * @member {chat.ChatMessageType} chatMessageType
         * @memberof chat.ChatMessage
         * @instance
         */
        ChatMessage.prototype.chatMessageType = 0;

        /**
         * ChatMessage senderId.
         * @member {string} senderId
         * @memberof chat.ChatMessage
         * @instance
         */
        ChatMessage.prototype.senderId = "";

        /**
         * ChatMessage receiverId.
         * @member {string} receiverId
         * @memberof chat.ChatMessage
         * @instance
         */
        ChatMessage.prototype.receiverId = "";

        /**
         * ChatMessage content.
         * @member {string} content
         * @memberof chat.ChatMessage
         * @instance
         */
        ChatMessage.prototype.content = "";

        /**
         * ChatMessage sequence.
         * @member {number|Long} sequence
         * @memberof chat.ChatMessage
         * @instance
         */
        ChatMessage.prototype.sequence = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * ChatMessage receiverType.
         * @member {chat.ReceiverType} receiverType
         * @memberof chat.ChatMessage
         * @instance
         */
        ChatMessage.prototype.receiverType = 0;

        /**
         * ChatMessage extra.
         * @member {string} extra
         * @memberof chat.ChatMessage
         * @instance
         */
        ChatMessage.prototype.extra = "";

        /**
         * Creates a new ChatMessage instance using the specified properties.
         * @function create
         * @memberof chat.ChatMessage
         * @static
         * @param {chat.IChatMessage=} [properties] Properties to set
         * @returns {chat.ChatMessage} ChatMessage instance
         */
        ChatMessage.create = function create(properties) {
            return new ChatMessage(properties);
        };

        /**
         * Encodes the specified ChatMessage message. Does not implicitly {@link chat.ChatMessage.verify|verify} messages.
         * @function encode
         * @memberof chat.ChatMessage
         * @static
         * @param {chat.IChatMessage} message ChatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.chatMessageType != null && Object.hasOwnProperty.call(message, "chatMessageType"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.chatMessageType);
            if (message.senderId != null && Object.hasOwnProperty.call(message, "senderId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.senderId);
            if (message.receiverId != null && Object.hasOwnProperty.call(message, "receiverId"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.receiverId);
            if (message.content != null && Object.hasOwnProperty.call(message, "content"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.content);
            if (message.sequence != null && Object.hasOwnProperty.call(message, "sequence"))
                writer.uint32(/* id 5, wireType 0 =*/40).int64(message.sequence);
            if (message.receiverType != null && Object.hasOwnProperty.call(message, "receiverType"))
                writer.uint32(/* id 6, wireType 0 =*/48).int32(message.receiverType);
            if (message.extra != null && Object.hasOwnProperty.call(message, "extra"))
                writer.uint32(/* id 7, wireType 2 =*/58).string(message.extra);
            return writer;
        };

        /**
         * Encodes the specified ChatMessage message, length delimited. Does not implicitly {@link chat.ChatMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof chat.ChatMessage
         * @static
         * @param {chat.IChatMessage} message ChatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ChatMessage message from the specified reader or buffer.
         * @function decode
         * @memberof chat.ChatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {chat.ChatMessage} ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatMessage.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.chat.ChatMessage();
            while (reader.pos < end) {
                let tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.chatMessageType = reader.int32();
                        break;
                    }
                case 2: {
                        message.senderId = reader.string();
                        break;
                    }
                case 3: {
                        message.receiverId = reader.string();
                        break;
                    }
                case 4: {
                        message.content = reader.string();
                        break;
                    }
                case 5: {
                        message.sequence = reader.int64();
                        break;
                    }
                case 6: {
                        message.receiverType = reader.int32();
                        break;
                    }
                case 7: {
                        message.extra = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ChatMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof chat.ChatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {chat.ChatMessage} ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ChatMessage message.
         * @function verify
         * @memberof chat.ChatMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ChatMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.chatMessageType != null && message.hasOwnProperty("chatMessageType"))
                switch (message.chatMessageType) {
                default:
                    return "chatMessageType: enum value expected";
                case 0:
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                    break;
                }
            if (message.senderId != null && message.hasOwnProperty("senderId"))
                if (!$util.isString(message.senderId))
                    return "senderId: string expected";
            if (message.receiverId != null && message.hasOwnProperty("receiverId"))
                if (!$util.isString(message.receiverId))
                    return "receiverId: string expected";
            if (message.content != null && message.hasOwnProperty("content"))
                if (!$util.isString(message.content))
                    return "content: string expected";
            if (message.sequence != null && message.hasOwnProperty("sequence"))
                if (!$util.isInteger(message.sequence) && !(message.sequence && $util.isInteger(message.sequence.low) && $util.isInteger(message.sequence.high)))
                    return "sequence: integer|Long expected";
            if (message.receiverType != null && message.hasOwnProperty("receiverType"))
                switch (message.receiverType) {
                default:
                    return "receiverType: enum value expected";
                case 0:
                case 1:
                case 2:
                case 3:
                case 4:
                    break;
                }
            if (message.extra != null && message.hasOwnProperty("extra"))
                if (!$util.isString(message.extra))
                    return "extra: string expected";
            return null;
        };

        /**
         * Creates a ChatMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof chat.ChatMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {chat.ChatMessage} ChatMessage
         */
        ChatMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.chat.ChatMessage)
                return object;
            let message = new $root.chat.ChatMessage();
            switch (object.chatMessageType) {
            default:
                if (typeof object.chatMessageType === "number") {
                    message.chatMessageType = object.chatMessageType;
                    break;
                }
                break;
            case "TEXT":
            case 0:
                message.chatMessageType = 0;
                break;
            case "AUDIO":
            case 1:
                message.chatMessageType = 1;
                break;
            case "IMAGE":
            case 2:
                message.chatMessageType = 2;
                break;
            case "VIDEO":
            case 3:
                message.chatMessageType = 3;
                break;
            case "FILE":
            case 4:
                message.chatMessageType = 4;
                break;
            case "GIFT":
            case 5:
                message.chatMessageType = 5;
                break;
            case "ENTER":
            case 6:
                message.chatMessageType = 6;
                break;
            }
            if (object.senderId != null)
                message.senderId = String(object.senderId);
            if (object.receiverId != null)
                message.receiverId = String(object.receiverId);
            if (object.content != null)
                message.content = String(object.content);
            if (object.sequence != null)
                if ($util.Long)
                    (message.sequence = $util.Long.fromValue(object.sequence)).unsigned = false;
                else if (typeof object.sequence === "string")
                    message.sequence = parseInt(object.sequence, 10);
                else if (typeof object.sequence === "number")
                    message.sequence = object.sequence;
                else if (typeof object.sequence === "object")
                    message.sequence = new $util.LongBits(object.sequence.low >>> 0, object.sequence.high >>> 0).toNumber();
            switch (object.receiverType) {
            default:
                if (typeof object.receiverType === "number") {
                    message.receiverType = object.receiverType;
                    break;
                }
                break;
            case "SINGLE":
            case 0:
                message.receiverType = 0;
                break;
            case "GROUP":
            case 1:
                message.receiverType = 1;
                break;
            case "CHAT_ROOM":
            case 2:
                message.receiverType = 2;
                break;
            case "SUPER_GROUP":
            case 3:
                message.receiverType = 3;
                break;
            case "LIVE_ROOM":
            case 4:
                message.receiverType = 4;
                break;
            }
            if (object.extra != null)
                message.extra = String(object.extra);
            return message;
        };

        /**
         * Creates a plain object from a ChatMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof chat.ChatMessage
         * @static
         * @param {chat.ChatMessage} message ChatMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ChatMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults) {
                object.chatMessageType = options.enums === String ? "TEXT" : 0;
                object.senderId = "";
                object.receiverId = "";
                object.content = "";
                if ($util.Long) {
                    let long = new $util.Long(0, 0, false);
                    object.sequence = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.sequence = options.longs === String ? "0" : 0;
                object.receiverType = options.enums === String ? "SINGLE" : 0;
                object.extra = "";
            }
            if (message.chatMessageType != null && message.hasOwnProperty("chatMessageType"))
                object.chatMessageType = options.enums === String ? $root.chat.ChatMessageType[message.chatMessageType] === undefined ? message.chatMessageType : $root.chat.ChatMessageType[message.chatMessageType] : message.chatMessageType;
            if (message.senderId != null && message.hasOwnProperty("senderId"))
                object.senderId = message.senderId;
            if (message.receiverId != null && message.hasOwnProperty("receiverId"))
                object.receiverId = message.receiverId;
            if (message.content != null && message.hasOwnProperty("content"))
                object.content = message.content;
            if (message.sequence != null && message.hasOwnProperty("sequence"))
                if (typeof message.sequence === "number")
                    object.sequence = options.longs === String ? String(message.sequence) : message.sequence;
                else
                    object.sequence = options.longs === String ? $util.Long.prototype.toString.call(message.sequence) : options.longs === Number ? new $util.LongBits(message.sequence.low >>> 0, message.sequence.high >>> 0).toNumber() : message.sequence;
            if (message.receiverType != null && message.hasOwnProperty("receiverType"))
                object.receiverType = options.enums === String ? $root.chat.ReceiverType[message.receiverType] === undefined ? message.receiverType : $root.chat.ReceiverType[message.receiverType] : message.receiverType;
            if (message.extra != null && message.hasOwnProperty("extra"))
                object.extra = message.extra;
            return object;
        };

        /**
         * Converts this ChatMessage to JSON.
         * @function toJSON
         * @memberof chat.ChatMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ChatMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ChatMessage
         * @function getTypeUrl
         * @memberof chat.ChatMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ChatMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/chat.ChatMessage";
        };

        return ChatMessage;
    })();

    chat.Attachment = (function() {

        /**
         * Properties of an Attachment.
         * @memberof chat
         * @interface IAttachment
         * @property {string|null} [url] Attachment url
         */

        /**
         * Constructs a new Attachment.
         * @memberof chat
         * @classdesc Represents an Attachment.
         * @implements IAttachment
         * @constructor
         * @param {chat.IAttachment=} [properties] Properties to set
         */
        function Attachment(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * Attachment url.
         * @member {string} url
         * @memberof chat.Attachment
         * @instance
         */
        Attachment.prototype.url = "";

        /**
         * Creates a new Attachment instance using the specified properties.
         * @function create
         * @memberof chat.Attachment
         * @static
         * @param {chat.IAttachment=} [properties] Properties to set
         * @returns {chat.Attachment} Attachment instance
         */
        Attachment.create = function create(properties) {
            return new Attachment(properties);
        };

        /**
         * Encodes the specified Attachment message. Does not implicitly {@link chat.Attachment.verify|verify} messages.
         * @function encode
         * @memberof chat.Attachment
         * @static
         * @param {chat.IAttachment} message Attachment message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        Attachment.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.url != null && Object.hasOwnProperty.call(message, "url"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.url);
            return writer;
        };

        /**
         * Encodes the specified Attachment message, length delimited. Does not implicitly {@link chat.Attachment.verify|verify} messages.
         * @function encodeDelimited
         * @memberof chat.Attachment
         * @static
         * @param {chat.IAttachment} message Attachment message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        Attachment.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an Attachment message from the specified reader or buffer.
         * @function decode
         * @memberof chat.Attachment
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {chat.Attachment} Attachment
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        Attachment.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.chat.Attachment();
            while (reader.pos < end) {
                let tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.url = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an Attachment message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof chat.Attachment
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {chat.Attachment} Attachment
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        Attachment.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an Attachment message.
         * @function verify
         * @memberof chat.Attachment
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        Attachment.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.url != null && message.hasOwnProperty("url"))
                if (!$util.isString(message.url))
                    return "url: string expected";
            return null;
        };

        /**
         * Creates an Attachment message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof chat.Attachment
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {chat.Attachment} Attachment
         */
        Attachment.fromObject = function fromObject(object) {
            if (object instanceof $root.chat.Attachment)
                return object;
            let message = new $root.chat.Attachment();
            if (object.url != null)
                message.url = String(object.url);
            return message;
        };

        /**
         * Creates a plain object from an Attachment message. Also converts values to other types if specified.
         * @function toObject
         * @memberof chat.Attachment
         * @static
         * @param {chat.Attachment} message Attachment
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        Attachment.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults)
                object.url = "";
            if (message.url != null && message.hasOwnProperty("url"))
                object.url = message.url;
            return object;
        };

        /**
         * Converts this Attachment to JSON.
         * @function toJSON
         * @memberof chat.Attachment
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        Attachment.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for Attachment
         * @function getTypeUrl
         * @memberof chat.Attachment
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        Attachment.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/chat.Attachment";
        };

        return Attachment;
    })();

    chat.MessageAck = (function() {

        /**
         * Properties of a MessageAck.
         * @memberof chat
         * @interface IMessageAck
         * @property {string|null} [messageId] MessageAck messageId
         * @property {boolean|null} [success] MessageAck success
         * @property {number|Long|null} [sequence] MessageAck sequence
         */

        /**
         * Constructs a new MessageAck.
         * @memberof chat
         * @classdesc Represents a MessageAck.
         * @implements IMessageAck
         * @constructor
         * @param {chat.IMessageAck=} [properties] Properties to set
         */
        function MessageAck(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * MessageAck messageId.
         * @member {string} messageId
         * @memberof chat.MessageAck
         * @instance
         */
        MessageAck.prototype.messageId = "";

        /**
         * MessageAck success.
         * @member {boolean} success
         * @memberof chat.MessageAck
         * @instance
         */
        MessageAck.prototype.success = false;

        /**
         * MessageAck sequence.
         * @member {number|Long} sequence
         * @memberof chat.MessageAck
         * @instance
         */
        MessageAck.prototype.sequence = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new MessageAck instance using the specified properties.
         * @function create
         * @memberof chat.MessageAck
         * @static
         * @param {chat.IMessageAck=} [properties] Properties to set
         * @returns {chat.MessageAck} MessageAck instance
         */
        MessageAck.create = function create(properties) {
            return new MessageAck(properties);
        };

        /**
         * Encodes the specified MessageAck message. Does not implicitly {@link chat.MessageAck.verify|verify} messages.
         * @function encode
         * @memberof chat.MessageAck
         * @static
         * @param {chat.IMessageAck} message MessageAck message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        MessageAck.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.messageId != null && Object.hasOwnProperty.call(message, "messageId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.messageId);
            if (message.success != null && Object.hasOwnProperty.call(message, "success"))
                writer.uint32(/* id 2, wireType 0 =*/16).bool(message.success);
            if (message.sequence != null && Object.hasOwnProperty.call(message, "sequence"))
                writer.uint32(/* id 3, wireType 0 =*/24).int64(message.sequence);
            return writer;
        };

        /**
         * Encodes the specified MessageAck message, length delimited. Does not implicitly {@link chat.MessageAck.verify|verify} messages.
         * @function encodeDelimited
         * @memberof chat.MessageAck
         * @static
         * @param {chat.IMessageAck} message MessageAck message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        MessageAck.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a MessageAck message from the specified reader or buffer.
         * @function decode
         * @memberof chat.MessageAck
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {chat.MessageAck} MessageAck
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        MessageAck.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.chat.MessageAck();
            while (reader.pos < end) {
                let tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.messageId = reader.string();
                        break;
                    }
                case 2: {
                        message.success = reader.bool();
                        break;
                    }
                case 3: {
                        message.sequence = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a MessageAck message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof chat.MessageAck
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {chat.MessageAck} MessageAck
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        MessageAck.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a MessageAck message.
         * @function verify
         * @memberof chat.MessageAck
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        MessageAck.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.messageId != null && message.hasOwnProperty("messageId"))
                if (!$util.isString(message.messageId))
                    return "messageId: string expected";
            if (message.success != null && message.hasOwnProperty("success"))
                if (typeof message.success !== "boolean")
                    return "success: boolean expected";
            if (message.sequence != null && message.hasOwnProperty("sequence"))
                if (!$util.isInteger(message.sequence) && !(message.sequence && $util.isInteger(message.sequence.low) && $util.isInteger(message.sequence.high)))
                    return "sequence: integer|Long expected";
            return null;
        };

        /**
         * Creates a MessageAck message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof chat.MessageAck
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {chat.MessageAck} MessageAck
         */
        MessageAck.fromObject = function fromObject(object) {
            if (object instanceof $root.chat.MessageAck)
                return object;
            let message = new $root.chat.MessageAck();
            if (object.messageId != null)
                message.messageId = String(object.messageId);
            if (object.success != null)
                message.success = Boolean(object.success);
            if (object.sequence != null)
                if ($util.Long)
                    (message.sequence = $util.Long.fromValue(object.sequence)).unsigned = false;
                else if (typeof object.sequence === "string")
                    message.sequence = parseInt(object.sequence, 10);
                else if (typeof object.sequence === "number")
                    message.sequence = object.sequence;
                else if (typeof object.sequence === "object")
                    message.sequence = new $util.LongBits(object.sequence.low >>> 0, object.sequence.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a MessageAck message. Also converts values to other types if specified.
         * @function toObject
         * @memberof chat.MessageAck
         * @static
         * @param {chat.MessageAck} message MessageAck
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        MessageAck.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults) {
                object.messageId = "";
                object.success = false;
                if ($util.Long) {
                    let long = new $util.Long(0, 0, false);
                    object.sequence = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.sequence = options.longs === String ? "0" : 0;
            }
            if (message.messageId != null && message.hasOwnProperty("messageId"))
                object.messageId = message.messageId;
            if (message.success != null && message.hasOwnProperty("success"))
                object.success = message.success;
            if (message.sequence != null && message.hasOwnProperty("sequence"))
                if (typeof message.sequence === "number")
                    object.sequence = options.longs === String ? String(message.sequence) : message.sequence;
                else
                    object.sequence = options.longs === String ? $util.Long.prototype.toString.call(message.sequence) : options.longs === Number ? new $util.LongBits(message.sequence.low >>> 0, message.sequence.high >>> 0).toNumber() : message.sequence;
            return object;
        };

        /**
         * Converts this MessageAck to JSON.
         * @function toJSON
         * @memberof chat.MessageAck
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        MessageAck.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for MessageAck
         * @function getTypeUrl
         * @memberof chat.MessageAck
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        MessageAck.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/chat.MessageAck";
        };

        return MessageAck;
    })();

    chat.HistoryMessages = (function() {

        /**
         * Properties of a HistoryMessages.
         * @memberof chat
         * @interface IHistoryMessages
         * @property {Array.<chat.IChatMessage>|null} [histories] HistoryMessages histories
         */

        /**
         * Constructs a new HistoryMessages.
         * @memberof chat
         * @classdesc Represents a HistoryMessages.
         * @implements IHistoryMessages
         * @constructor
         * @param {chat.IHistoryMessages=} [properties] Properties to set
         */
        function HistoryMessages(properties) {
            this.histories = [];
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * HistoryMessages histories.
         * @member {Array.<chat.IChatMessage>} histories
         * @memberof chat.HistoryMessages
         * @instance
         */
        HistoryMessages.prototype.histories = $util.emptyArray;

        /**
         * Creates a new HistoryMessages instance using the specified properties.
         * @function create
         * @memberof chat.HistoryMessages
         * @static
         * @param {chat.IHistoryMessages=} [properties] Properties to set
         * @returns {chat.HistoryMessages} HistoryMessages instance
         */
        HistoryMessages.create = function create(properties) {
            return new HistoryMessages(properties);
        };

        /**
         * Encodes the specified HistoryMessages message. Does not implicitly {@link chat.HistoryMessages.verify|verify} messages.
         * @function encode
         * @memberof chat.HistoryMessages
         * @static
         * @param {chat.IHistoryMessages} message HistoryMessages message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        HistoryMessages.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.histories != null && message.histories.length)
                for (let i = 0; i < message.histories.length; ++i)
                    $root.chat.ChatMessage.encode(message.histories[i], writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified HistoryMessages message, length delimited. Does not implicitly {@link chat.HistoryMessages.verify|verify} messages.
         * @function encodeDelimited
         * @memberof chat.HistoryMessages
         * @static
         * @param {chat.IHistoryMessages} message HistoryMessages message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        HistoryMessages.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a HistoryMessages message from the specified reader or buffer.
         * @function decode
         * @memberof chat.HistoryMessages
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {chat.HistoryMessages} HistoryMessages
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        HistoryMessages.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.chat.HistoryMessages();
            while (reader.pos < end) {
                let tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        if (!(message.histories && message.histories.length))
                            message.histories = [];
                        message.histories.push($root.chat.ChatMessage.decode(reader, reader.uint32()));
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a HistoryMessages message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof chat.HistoryMessages
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {chat.HistoryMessages} HistoryMessages
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        HistoryMessages.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a HistoryMessages message.
         * @function verify
         * @memberof chat.HistoryMessages
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        HistoryMessages.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.histories != null && message.hasOwnProperty("histories")) {
                if (!Array.isArray(message.histories))
                    return "histories: array expected";
                for (let i = 0; i < message.histories.length; ++i) {
                    let error = $root.chat.ChatMessage.verify(message.histories[i]);
                    if (error)
                        return "histories." + error;
                }
            }
            return null;
        };

        /**
         * Creates a HistoryMessages message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof chat.HistoryMessages
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {chat.HistoryMessages} HistoryMessages
         */
        HistoryMessages.fromObject = function fromObject(object) {
            if (object instanceof $root.chat.HistoryMessages)
                return object;
            let message = new $root.chat.HistoryMessages();
            if (object.histories) {
                if (!Array.isArray(object.histories))
                    throw TypeError(".chat.HistoryMessages.histories: array expected");
                message.histories = [];
                for (let i = 0; i < object.histories.length; ++i) {
                    if (typeof object.histories[i] !== "object")
                        throw TypeError(".chat.HistoryMessages.histories: object expected");
                    message.histories[i] = $root.chat.ChatMessage.fromObject(object.histories[i]);
                }
            }
            return message;
        };

        /**
         * Creates a plain object from a HistoryMessages message. Also converts values to other types if specified.
         * @function toObject
         * @memberof chat.HistoryMessages
         * @static
         * @param {chat.HistoryMessages} message HistoryMessages
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        HistoryMessages.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.arrays || options.defaults)
                object.histories = [];
            if (message.histories && message.histories.length) {
                object.histories = [];
                for (let j = 0; j < message.histories.length; ++j)
                    object.histories[j] = $root.chat.ChatMessage.toObject(message.histories[j], options);
            }
            return object;
        };

        /**
         * Converts this HistoryMessages to JSON.
         * @function toJSON
         * @memberof chat.HistoryMessages
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        HistoryMessages.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for HistoryMessages
         * @function getTypeUrl
         * @memberof chat.HistoryMessages
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        HistoryMessages.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/chat.HistoryMessages";
        };

        return HistoryMessages;
    })();

    return chat;
})();

export const IMMessage = $root.IMMessage = (() => {

    /**
     * Properties of a IMMessage.
     * @exports IIMMessage
     * @interface IIMMessage
     * @property {IHeader|null} [header] IMMessage header
     * @property {auth.ILoginRequest|null} [loginRequest] IMMessage loginRequest
     * @property {auth.ILoginResponse|null} [loginResponse] IMMessage loginResponse
     * @property {auth.ILogoutRequest|null} [logoutRequest] IMMessage logoutRequest
     * @property {auth.ILogoutResponse|null} [logoutResponse] IMMessage logoutResponse
     * @property {chat.IChatMessage|null} [chatMessage] IMMessage chatMessage
     * @property {chat.IMessageAck|null} [messageAck] IMMessage messageAck
     * @property {chat.IHistoryMessages|null} [historyMessages] IMMessage historyMessages
     * @property {live.IEnterLiveRoomEvent|null} [enterLiveRoomEvent] IMMessage enterLiveRoomEvent
     * @property {live.IExitLiveRoomEvent|null} [exitLiveRoomEvent] IMMessage exitLiveRoomEvent
     * @property {string|null} [heartbeat] IMMessage heartbeat
     */

    /**
     * Constructs a new IMMessage.
     * @exports IMMessage
     * @classdesc Represents a IMMessage.
     * @implements IIMMessage
     * @constructor
     * @param {IIMMessage=} [properties] Properties to set
     */
    function IMMessage(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * IMMessage header.
     * @member {IHeader|null|undefined} header
     * @memberof IMMessage
     * @instance
     */
    IMMessage.prototype.header = null;

    /**
     * IMMessage loginRequest.
     * @member {auth.ILoginRequest|null|undefined} loginRequest
     * @memberof IMMessage
     * @instance
     */
    IMMessage.prototype.loginRequest = null;

    /**
     * IMMessage loginResponse.
     * @member {auth.ILoginResponse|null|undefined} loginResponse
     * @memberof IMMessage
     * @instance
     */
    IMMessage.prototype.loginResponse = null;

    /**
     * IMMessage logoutRequest.
     * @member {auth.ILogoutRequest|null|undefined} logoutRequest
     * @memberof IMMessage
     * @instance
     */
    IMMessage.prototype.logoutRequest = null;

    /**
     * IMMessage logoutResponse.
     * @member {auth.ILogoutResponse|null|undefined} logoutResponse
     * @memberof IMMessage
     * @instance
     */
    IMMessage.prototype.logoutResponse = null;

    /**
     * IMMessage chatMessage.
     * @member {chat.IChatMessage|null|undefined} chatMessage
     * @memberof IMMessage
     * @instance
     */
    IMMessage.prototype.chatMessage = null;

    /**
     * IMMessage messageAck.
     * @member {chat.IMessageAck|null|undefined} messageAck
     * @memberof IMMessage
     * @instance
     */
    IMMessage.prototype.messageAck = null;

    /**
     * IMMessage historyMessages.
     * @member {chat.IHistoryMessages|null|undefined} historyMessages
     * @memberof IMMessage
     * @instance
     */
    IMMessage.prototype.historyMessages = null;

    /**
     * IMMessage enterLiveRoomEvent.
     * @member {live.IEnterLiveRoomEvent|null|undefined} enterLiveRoomEvent
     * @memberof IMMessage
     * @instance
     */
    IMMessage.prototype.enterLiveRoomEvent = null;

    /**
     * IMMessage exitLiveRoomEvent.
     * @member {live.IExitLiveRoomEvent|null|undefined} exitLiveRoomEvent
     * @memberof IMMessage
     * @instance
     */
    IMMessage.prototype.exitLiveRoomEvent = null;

    /**
     * IMMessage heartbeat.
     * @member {string|null|undefined} heartbeat
     * @memberof IMMessage
     * @instance
     */
    IMMessage.prototype.heartbeat = null;

    // OneOf field names bound to virtual getters and setters
    let $oneOfFields;

    /**
     * IMMessage body.
     * @member {"loginRequest"|"loginResponse"|"logoutRequest"|"logoutResponse"|"chatMessage"|"messageAck"|"historyMessages"|"enterLiveRoomEvent"|"exitLiveRoomEvent"|"heartbeat"|undefined} body
     * @memberof IMMessage
     * @instance
     */
    Object.defineProperty(IMMessage.prototype, "body", {
        get: $util.oneOfGetter($oneOfFields = ["loginRequest", "loginResponse", "logoutRequest", "logoutResponse", "chatMessage", "messageAck", "historyMessages", "enterLiveRoomEvent", "exitLiveRoomEvent", "heartbeat"]),
        set: $util.oneOfSetter($oneOfFields)
    });

    /**
     * Creates a new IMMessage instance using the specified properties.
     * @function create
     * @memberof IMMessage
     * @static
     * @param {IIMMessage=} [properties] Properties to set
     * @returns {IMMessage} IMMessage instance
     */
    IMMessage.create = function create(properties) {
        return new IMMessage(properties);
    };

    /**
     * Encodes the specified IMMessage message. Does not implicitly {@link IMMessage.verify|verify} messages.
     * @function encode
     * @memberof IMMessage
     * @static
     * @param {IIMMessage} message IMMessage message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    IMMessage.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.header != null && Object.hasOwnProperty.call(message, "header"))
            $root.Header.encode(message.header, writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();
        if (message.heartbeat != null && Object.hasOwnProperty.call(message, "heartbeat"))
            writer.uint32(/* id 6, wireType 2 =*/50).string(message.heartbeat);
        if (message.loginRequest != null && Object.hasOwnProperty.call(message, "loginRequest"))
            $root.auth.LoginRequest.encode(message.loginRequest, writer.uint32(/* id 1001, wireType 2 =*/8010).fork()).ldelim();
        if (message.loginResponse != null && Object.hasOwnProperty.call(message, "loginResponse"))
            $root.auth.LoginResponse.encode(message.loginResponse, writer.uint32(/* id 1002, wireType 2 =*/8018).fork()).ldelim();
        if (message.logoutRequest != null && Object.hasOwnProperty.call(message, "logoutRequest"))
            $root.auth.LogoutRequest.encode(message.logoutRequest, writer.uint32(/* id 1003, wireType 2 =*/8026).fork()).ldelim();
        if (message.logoutResponse != null && Object.hasOwnProperty.call(message, "logoutResponse"))
            $root.auth.LogoutResponse.encode(message.logoutResponse, writer.uint32(/* id 1004, wireType 2 =*/8034).fork()).ldelim();
        if (message.chatMessage != null && Object.hasOwnProperty.call(message, "chatMessage"))
            $root.chat.ChatMessage.encode(message.chatMessage, writer.uint32(/* id 2001, wireType 2 =*/16010).fork()).ldelim();
        if (message.messageAck != null && Object.hasOwnProperty.call(message, "messageAck"))
            $root.chat.MessageAck.encode(message.messageAck, writer.uint32(/* id 2002, wireType 2 =*/16018).fork()).ldelim();
        if (message.historyMessages != null && Object.hasOwnProperty.call(message, "historyMessages"))
            $root.chat.HistoryMessages.encode(message.historyMessages, writer.uint32(/* id 2003, wireType 2 =*/16026).fork()).ldelim();
        if (message.enterLiveRoomEvent != null && Object.hasOwnProperty.call(message, "enterLiveRoomEvent"))
            $root.live.EnterLiveRoomEvent.encode(message.enterLiveRoomEvent, writer.uint32(/* id 3001, wireType 2 =*/24010).fork()).ldelim();
        if (message.exitLiveRoomEvent != null && Object.hasOwnProperty.call(message, "exitLiveRoomEvent"))
            $root.live.ExitLiveRoomEvent.encode(message.exitLiveRoomEvent, writer.uint32(/* id 3002, wireType 2 =*/24018).fork()).ldelim();
        return writer;
    };

    /**
     * Encodes the specified IMMessage message, length delimited. Does not implicitly {@link IMMessage.verify|verify} messages.
     * @function encodeDelimited
     * @memberof IMMessage
     * @static
     * @param {IIMMessage} message IMMessage message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    IMMessage.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a IMMessage message from the specified reader or buffer.
     * @function decode
     * @memberof IMMessage
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {IMMessage} IMMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    IMMessage.decode = function decode(reader, length) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.IMMessage();
        while (reader.pos < end) {
            let tag = reader.uint32();
            switch (tag >>> 3) {
            case 1: {
                    message.header = $root.Header.decode(reader, reader.uint32());
                    break;
                }
            case 1001: {
                    message.loginRequest = $root.auth.LoginRequest.decode(reader, reader.uint32());
                    break;
                }
            case 1002: {
                    message.loginResponse = $root.auth.LoginResponse.decode(reader, reader.uint32());
                    break;
                }
            case 1003: {
                    message.logoutRequest = $root.auth.LogoutRequest.decode(reader, reader.uint32());
                    break;
                }
            case 1004: {
                    message.logoutResponse = $root.auth.LogoutResponse.decode(reader, reader.uint32());
                    break;
                }
            case 2001: {
                    message.chatMessage = $root.chat.ChatMessage.decode(reader, reader.uint32());
                    break;
                }
            case 2002: {
                    message.messageAck = $root.chat.MessageAck.decode(reader, reader.uint32());
                    break;
                }
            case 2003: {
                    message.historyMessages = $root.chat.HistoryMessages.decode(reader, reader.uint32());
                    break;
                }
            case 3001: {
                    message.enterLiveRoomEvent = $root.live.EnterLiveRoomEvent.decode(reader, reader.uint32());
                    break;
                }
            case 3002: {
                    message.exitLiveRoomEvent = $root.live.ExitLiveRoomEvent.decode(reader, reader.uint32());
                    break;
                }
            case 6: {
                    message.heartbeat = reader.string();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a IMMessage message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof IMMessage
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {IMMessage} IMMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    IMMessage.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a IMMessage message.
     * @function verify
     * @memberof IMMessage
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    IMMessage.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        let properties = {};
        if (message.header != null && message.hasOwnProperty("header")) {
            let error = $root.Header.verify(message.header);
            if (error)
                return "header." + error;
        }
        if (message.loginRequest != null && message.hasOwnProperty("loginRequest")) {
            properties.body = 1;
            {
                let error = $root.auth.LoginRequest.verify(message.loginRequest);
                if (error)
                    return "loginRequest." + error;
            }
        }
        if (message.loginResponse != null && message.hasOwnProperty("loginResponse")) {
            if (properties.body === 1)
                return "body: multiple values";
            properties.body = 1;
            {
                let error = $root.auth.LoginResponse.verify(message.loginResponse);
                if (error)
                    return "loginResponse." + error;
            }
        }
        if (message.logoutRequest != null && message.hasOwnProperty("logoutRequest")) {
            if (properties.body === 1)
                return "body: multiple values";
            properties.body = 1;
            {
                let error = $root.auth.LogoutRequest.verify(message.logoutRequest);
                if (error)
                    return "logoutRequest." + error;
            }
        }
        if (message.logoutResponse != null && message.hasOwnProperty("logoutResponse")) {
            if (properties.body === 1)
                return "body: multiple values";
            properties.body = 1;
            {
                let error = $root.auth.LogoutResponse.verify(message.logoutResponse);
                if (error)
                    return "logoutResponse." + error;
            }
        }
        if (message.chatMessage != null && message.hasOwnProperty("chatMessage")) {
            if (properties.body === 1)
                return "body: multiple values";
            properties.body = 1;
            {
                let error = $root.chat.ChatMessage.verify(message.chatMessage);
                if (error)
                    return "chatMessage." + error;
            }
        }
        if (message.messageAck != null && message.hasOwnProperty("messageAck")) {
            if (properties.body === 1)
                return "body: multiple values";
            properties.body = 1;
            {
                let error = $root.chat.MessageAck.verify(message.messageAck);
                if (error)
                    return "messageAck." + error;
            }
        }
        if (message.historyMessages != null && message.hasOwnProperty("historyMessages")) {
            if (properties.body === 1)
                return "body: multiple values";
            properties.body = 1;
            {
                let error = $root.chat.HistoryMessages.verify(message.historyMessages);
                if (error)
                    return "historyMessages." + error;
            }
        }
        if (message.enterLiveRoomEvent != null && message.hasOwnProperty("enterLiveRoomEvent")) {
            if (properties.body === 1)
                return "body: multiple values";
            properties.body = 1;
            {
                let error = $root.live.EnterLiveRoomEvent.verify(message.enterLiveRoomEvent);
                if (error)
                    return "enterLiveRoomEvent." + error;
            }
        }
        if (message.exitLiveRoomEvent != null && message.hasOwnProperty("exitLiveRoomEvent")) {
            if (properties.body === 1)
                return "body: multiple values";
            properties.body = 1;
            {
                let error = $root.live.ExitLiveRoomEvent.verify(message.exitLiveRoomEvent);
                if (error)
                    return "exitLiveRoomEvent." + error;
            }
        }
        if (message.heartbeat != null && message.hasOwnProperty("heartbeat")) {
            if (properties.body === 1)
                return "body: multiple values";
            properties.body = 1;
            if (!$util.isString(message.heartbeat))
                return "heartbeat: string expected";
        }
        return null;
    };

    /**
     * Creates a IMMessage message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof IMMessage
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {IMMessage} IMMessage
     */
    IMMessage.fromObject = function fromObject(object) {
        if (object instanceof $root.IMMessage)
            return object;
        let message = new $root.IMMessage();
        if (object.header != null) {
            if (typeof object.header !== "object")
                throw TypeError(".IMMessage.header: object expected");
            message.header = $root.Header.fromObject(object.header);
        }
        if (object.loginRequest != null) {
            if (typeof object.loginRequest !== "object")
                throw TypeError(".IMMessage.loginRequest: object expected");
            message.loginRequest = $root.auth.LoginRequest.fromObject(object.loginRequest);
        }
        if (object.loginResponse != null) {
            if (typeof object.loginResponse !== "object")
                throw TypeError(".IMMessage.loginResponse: object expected");
            message.loginResponse = $root.auth.LoginResponse.fromObject(object.loginResponse);
        }
        if (object.logoutRequest != null) {
            if (typeof object.logoutRequest !== "object")
                throw TypeError(".IMMessage.logoutRequest: object expected");
            message.logoutRequest = $root.auth.LogoutRequest.fromObject(object.logoutRequest);
        }
        if (object.logoutResponse != null) {
            if (typeof object.logoutResponse !== "object")
                throw TypeError(".IMMessage.logoutResponse: object expected");
            message.logoutResponse = $root.auth.LogoutResponse.fromObject(object.logoutResponse);
        }
        if (object.chatMessage != null) {
            if (typeof object.chatMessage !== "object")
                throw TypeError(".IMMessage.chatMessage: object expected");
            message.chatMessage = $root.chat.ChatMessage.fromObject(object.chatMessage);
        }
        if (object.messageAck != null) {
            if (typeof object.messageAck !== "object")
                throw TypeError(".IMMessage.messageAck: object expected");
            message.messageAck = $root.chat.MessageAck.fromObject(object.messageAck);
        }
        if (object.historyMessages != null) {
            if (typeof object.historyMessages !== "object")
                throw TypeError(".IMMessage.historyMessages: object expected");
            message.historyMessages = $root.chat.HistoryMessages.fromObject(object.historyMessages);
        }
        if (object.enterLiveRoomEvent != null) {
            if (typeof object.enterLiveRoomEvent !== "object")
                throw TypeError(".IMMessage.enterLiveRoomEvent: object expected");
            message.enterLiveRoomEvent = $root.live.EnterLiveRoomEvent.fromObject(object.enterLiveRoomEvent);
        }
        if (object.exitLiveRoomEvent != null) {
            if (typeof object.exitLiveRoomEvent !== "object")
                throw TypeError(".IMMessage.exitLiveRoomEvent: object expected");
            message.exitLiveRoomEvent = $root.live.ExitLiveRoomEvent.fromObject(object.exitLiveRoomEvent);
        }
        if (object.heartbeat != null)
            message.heartbeat = String(object.heartbeat);
        return message;
    };

    /**
     * Creates a plain object from a IMMessage message. Also converts values to other types if specified.
     * @function toObject
     * @memberof IMMessage
     * @static
     * @param {IMMessage} message IMMessage
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    IMMessage.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults)
            object.header = null;
        if (message.header != null && message.hasOwnProperty("header"))
            object.header = $root.Header.toObject(message.header, options);
        if (message.heartbeat != null && message.hasOwnProperty("heartbeat")) {
            object.heartbeat = message.heartbeat;
            if (options.oneofs)
                object.body = "heartbeat";
        }
        if (message.loginRequest != null && message.hasOwnProperty("loginRequest")) {
            object.loginRequest = $root.auth.LoginRequest.toObject(message.loginRequest, options);
            if (options.oneofs)
                object.body = "loginRequest";
        }
        if (message.loginResponse != null && message.hasOwnProperty("loginResponse")) {
            object.loginResponse = $root.auth.LoginResponse.toObject(message.loginResponse, options);
            if (options.oneofs)
                object.body = "loginResponse";
        }
        if (message.logoutRequest != null && message.hasOwnProperty("logoutRequest")) {
            object.logoutRequest = $root.auth.LogoutRequest.toObject(message.logoutRequest, options);
            if (options.oneofs)
                object.body = "logoutRequest";
        }
        if (message.logoutResponse != null && message.hasOwnProperty("logoutResponse")) {
            object.logoutResponse = $root.auth.LogoutResponse.toObject(message.logoutResponse, options);
            if (options.oneofs)
                object.body = "logoutResponse";
        }
        if (message.chatMessage != null && message.hasOwnProperty("chatMessage")) {
            object.chatMessage = $root.chat.ChatMessage.toObject(message.chatMessage, options);
            if (options.oneofs)
                object.body = "chatMessage";
        }
        if (message.messageAck != null && message.hasOwnProperty("messageAck")) {
            object.messageAck = $root.chat.MessageAck.toObject(message.messageAck, options);
            if (options.oneofs)
                object.body = "messageAck";
        }
        if (message.historyMessages != null && message.hasOwnProperty("historyMessages")) {
            object.historyMessages = $root.chat.HistoryMessages.toObject(message.historyMessages, options);
            if (options.oneofs)
                object.body = "historyMessages";
        }
        if (message.enterLiveRoomEvent != null && message.hasOwnProperty("enterLiveRoomEvent")) {
            object.enterLiveRoomEvent = $root.live.EnterLiveRoomEvent.toObject(message.enterLiveRoomEvent, options);
            if (options.oneofs)
                object.body = "enterLiveRoomEvent";
        }
        if (message.exitLiveRoomEvent != null && message.hasOwnProperty("exitLiveRoomEvent")) {
            object.exitLiveRoomEvent = $root.live.ExitLiveRoomEvent.toObject(message.exitLiveRoomEvent, options);
            if (options.oneofs)
                object.body = "exitLiveRoomEvent";
        }
        return object;
    };

    /**
     * Converts this IMMessage to JSON.
     * @function toJSON
     * @memberof IMMessage
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    IMMessage.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for IMMessage
     * @function getTypeUrl
     * @memberof IMMessage
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    IMMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/IMMessage";
    };

    return IMMessage;
})();

export const Header = $root.Header = (() => {

    /**
     * Properties of a Header.
     * @exports IHeader
     * @interface IHeader
     * @property {string|null} [messageId] Header messageId
     * @property {number|Long|null} [timestamp] Header timestamp
     * @property {number|null} [version] Header version
     * @property {common.IUser|null} [user] Header user
     */

    /**
     * Constructs a new Header.
     * @exports Header
     * @classdesc Represents a Header.
     * @implements IHeader
     * @constructor
     * @param {IHeader=} [properties] Properties to set
     */
    function Header(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * Header messageId.
     * @member {string} messageId
     * @memberof Header
     * @instance
     */
    Header.prototype.messageId = "";

    /**
     * Header timestamp.
     * @member {number|Long} timestamp
     * @memberof Header
     * @instance
     */
    Header.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

    /**
     * Header version.
     * @member {number} version
     * @memberof Header
     * @instance
     */
    Header.prototype.version = 0;

    /**
     * Header user.
     * @member {common.IUser|null|undefined} user
     * @memberof Header
     * @instance
     */
    Header.prototype.user = null;

    /**
     * Creates a new Header instance using the specified properties.
     * @function create
     * @memberof Header
     * @static
     * @param {IHeader=} [properties] Properties to set
     * @returns {Header} Header instance
     */
    Header.create = function create(properties) {
        return new Header(properties);
    };

    /**
     * Encodes the specified Header message. Does not implicitly {@link Header.verify|verify} messages.
     * @function encode
     * @memberof Header
     * @static
     * @param {IHeader} message Header message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    Header.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.messageId != null && Object.hasOwnProperty.call(message, "messageId"))
            writer.uint32(/* id 1, wireType 2 =*/10).string(message.messageId);
        if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
            writer.uint32(/* id 2, wireType 0 =*/16).int64(message.timestamp);
        if (message.version != null && Object.hasOwnProperty.call(message, "version"))
            writer.uint32(/* id 3, wireType 0 =*/24).int32(message.version);
        if (message.user != null && Object.hasOwnProperty.call(message, "user"))
            $root.common.User.encode(message.user, writer.uint32(/* id 4, wireType 2 =*/34).fork()).ldelim();
        return writer;
    };

    /**
     * Encodes the specified Header message, length delimited. Does not implicitly {@link Header.verify|verify} messages.
     * @function encodeDelimited
     * @memberof Header
     * @static
     * @param {IHeader} message Header message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    Header.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a Header message from the specified reader or buffer.
     * @function decode
     * @memberof Header
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {Header} Header
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    Header.decode = function decode(reader, length) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.Header();
        while (reader.pos < end) {
            let tag = reader.uint32();
            switch (tag >>> 3) {
            case 1: {
                    message.messageId = reader.string();
                    break;
                }
            case 2: {
                    message.timestamp = reader.int64();
                    break;
                }
            case 3: {
                    message.version = reader.int32();
                    break;
                }
            case 4: {
                    message.user = $root.common.User.decode(reader, reader.uint32());
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a Header message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof Header
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {Header} Header
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    Header.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a Header message.
     * @function verify
     * @memberof Header
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    Header.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.messageId != null && message.hasOwnProperty("messageId"))
            if (!$util.isString(message.messageId))
                return "messageId: string expected";
        if (message.timestamp != null && message.hasOwnProperty("timestamp"))
            if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                return "timestamp: integer|Long expected";
        if (message.version != null && message.hasOwnProperty("version"))
            if (!$util.isInteger(message.version))
                return "version: integer expected";
        if (message.user != null && message.hasOwnProperty("user")) {
            let error = $root.common.User.verify(message.user);
            if (error)
                return "user." + error;
        }
        return null;
    };

    /**
     * Creates a Header message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof Header
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {Header} Header
     */
    Header.fromObject = function fromObject(object) {
        if (object instanceof $root.Header)
            return object;
        let message = new $root.Header();
        if (object.messageId != null)
            message.messageId = String(object.messageId);
        if (object.timestamp != null)
            if ($util.Long)
                (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
            else if (typeof object.timestamp === "string")
                message.timestamp = parseInt(object.timestamp, 10);
            else if (typeof object.timestamp === "number")
                message.timestamp = object.timestamp;
            else if (typeof object.timestamp === "object")
                message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
        if (object.version != null)
            message.version = object.version | 0;
        if (object.user != null) {
            if (typeof object.user !== "object")
                throw TypeError(".Header.user: object expected");
            message.user = $root.common.User.fromObject(object.user);
        }
        return message;
    };

    /**
     * Creates a plain object from a Header message. Also converts values to other types if specified.
     * @function toObject
     * @memberof Header
     * @static
     * @param {Header} message Header
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    Header.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults) {
            object.messageId = "";
            if ($util.Long) {
                let long = new $util.Long(0, 0, false);
                object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
            } else
                object.timestamp = options.longs === String ? "0" : 0;
            object.version = 0;
            object.user = null;
        }
        if (message.messageId != null && message.hasOwnProperty("messageId"))
            object.messageId = message.messageId;
        if (message.timestamp != null && message.hasOwnProperty("timestamp"))
            if (typeof message.timestamp === "number")
                object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
            else
                object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
        if (message.version != null && message.hasOwnProperty("version"))
            object.version = message.version;
        if (message.user != null && message.hasOwnProperty("user"))
            object.user = $root.common.User.toObject(message.user, options);
        return object;
    };

    /**
     * Converts this Header to JSON.
     * @function toJSON
     * @memberof Header
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    Header.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for Header
     * @function getTypeUrl
     * @memberof Header
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    Header.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/Header";
    };

    return Header;
})();

export const live = $root.live = (() => {

    /**
     * Namespace live.
     * @exports live
     * @namespace
     */
    const live = {};

    live.EnterLiveRoomEvent = (function() {

        /**
         * Properties of an EnterLiveRoomEvent.
         * @memberof live
         * @interface IEnterLiveRoomEvent
         * @property {string|null} [roomId] EnterLiveRoomEvent roomId
         * @property {live.UserType|null} [userType] EnterLiveRoomEvent userType
         */

        /**
         * Constructs a new EnterLiveRoomEvent.
         * @memberof live
         * @classdesc Represents an EnterLiveRoomEvent.
         * @implements IEnterLiveRoomEvent
         * @constructor
         * @param {live.IEnterLiveRoomEvent=} [properties] Properties to set
         */
        function EnterLiveRoomEvent(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * EnterLiveRoomEvent roomId.
         * @member {string} roomId
         * @memberof live.EnterLiveRoomEvent
         * @instance
         */
        EnterLiveRoomEvent.prototype.roomId = "";

        /**
         * EnterLiveRoomEvent userType.
         * @member {live.UserType} userType
         * @memberof live.EnterLiveRoomEvent
         * @instance
         */
        EnterLiveRoomEvent.prototype.userType = 0;

        /**
         * Creates a new EnterLiveRoomEvent instance using the specified properties.
         * @function create
         * @memberof live.EnterLiveRoomEvent
         * @static
         * @param {live.IEnterLiveRoomEvent=} [properties] Properties to set
         * @returns {live.EnterLiveRoomEvent} EnterLiveRoomEvent instance
         */
        EnterLiveRoomEvent.create = function create(properties) {
            return new EnterLiveRoomEvent(properties);
        };

        /**
         * Encodes the specified EnterLiveRoomEvent message. Does not implicitly {@link live.EnterLiveRoomEvent.verify|verify} messages.
         * @function encode
         * @memberof live.EnterLiveRoomEvent
         * @static
         * @param {live.IEnterLiveRoomEvent} message EnterLiveRoomEvent message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        EnterLiveRoomEvent.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.roomId != null && Object.hasOwnProperty.call(message, "roomId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.roomId);
            if (message.userType != null && Object.hasOwnProperty.call(message, "userType"))
                writer.uint32(/* id 2, wireType 0 =*/16).int32(message.userType);
            return writer;
        };

        /**
         * Encodes the specified EnterLiveRoomEvent message, length delimited. Does not implicitly {@link live.EnterLiveRoomEvent.verify|verify} messages.
         * @function encodeDelimited
         * @memberof live.EnterLiveRoomEvent
         * @static
         * @param {live.IEnterLiveRoomEvent} message EnterLiveRoomEvent message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        EnterLiveRoomEvent.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an EnterLiveRoomEvent message from the specified reader or buffer.
         * @function decode
         * @memberof live.EnterLiveRoomEvent
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {live.EnterLiveRoomEvent} EnterLiveRoomEvent
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        EnterLiveRoomEvent.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.live.EnterLiveRoomEvent();
            while (reader.pos < end) {
                let tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.roomId = reader.string();
                        break;
                    }
                case 2: {
                        message.userType = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an EnterLiveRoomEvent message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof live.EnterLiveRoomEvent
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {live.EnterLiveRoomEvent} EnterLiveRoomEvent
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        EnterLiveRoomEvent.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an EnterLiveRoomEvent message.
         * @function verify
         * @memberof live.EnterLiveRoomEvent
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        EnterLiveRoomEvent.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.roomId != null && message.hasOwnProperty("roomId"))
                if (!$util.isString(message.roomId))
                    return "roomId: string expected";
            if (message.userType != null && message.hasOwnProperty("userType"))
                switch (message.userType) {
                default:
                    return "userType: enum value expected";
                case 0:
                case 1:
                    break;
                }
            return null;
        };

        /**
         * Creates an EnterLiveRoomEvent message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof live.EnterLiveRoomEvent
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {live.EnterLiveRoomEvent} EnterLiveRoomEvent
         */
        EnterLiveRoomEvent.fromObject = function fromObject(object) {
            if (object instanceof $root.live.EnterLiveRoomEvent)
                return object;
            let message = new $root.live.EnterLiveRoomEvent();
            if (object.roomId != null)
                message.roomId = String(object.roomId);
            switch (object.userType) {
            default:
                if (typeof object.userType === "number") {
                    message.userType = object.userType;
                    break;
                }
                break;
            case "ANONYMOUS":
            case 0:
                message.userType = 0;
                break;
            case "SIGNED":
            case 1:
                message.userType = 1;
                break;
            }
            return message;
        };

        /**
         * Creates a plain object from an EnterLiveRoomEvent message. Also converts values to other types if specified.
         * @function toObject
         * @memberof live.EnterLiveRoomEvent
         * @static
         * @param {live.EnterLiveRoomEvent} message EnterLiveRoomEvent
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        EnterLiveRoomEvent.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults) {
                object.roomId = "";
                object.userType = options.enums === String ? "ANONYMOUS" : 0;
            }
            if (message.roomId != null && message.hasOwnProperty("roomId"))
                object.roomId = message.roomId;
            if (message.userType != null && message.hasOwnProperty("userType"))
                object.userType = options.enums === String ? $root.live.UserType[message.userType] === undefined ? message.userType : $root.live.UserType[message.userType] : message.userType;
            return object;
        };

        /**
         * Converts this EnterLiveRoomEvent to JSON.
         * @function toJSON
         * @memberof live.EnterLiveRoomEvent
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        EnterLiveRoomEvent.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for EnterLiveRoomEvent
         * @function getTypeUrl
         * @memberof live.EnterLiveRoomEvent
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        EnterLiveRoomEvent.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/live.EnterLiveRoomEvent";
        };

        return EnterLiveRoomEvent;
    })();

    /**
     * UserType enum.
     * @name live.UserType
     * @enum {number}
     * @property {number} ANONYMOUS=0 ANONYMOUS value
     * @property {number} SIGNED=1 SIGNED value
     */
    live.UserType = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "ANONYMOUS"] = 0;
        values[valuesById[1] = "SIGNED"] = 1;
        return values;
    })();

    live.ExitLiveRoomEvent = (function() {

        /**
         * Properties of an ExitLiveRoomEvent.
         * @memberof live
         * @interface IExitLiveRoomEvent
         * @property {boolean|null} [success] ExitLiveRoomEvent success
         * @property {string|null} [errorMessage] ExitLiveRoomEvent errorMessage
         * @property {string|null} [roomId] ExitLiveRoomEvent roomId
         */

        /**
         * Constructs a new ExitLiveRoomEvent.
         * @memberof live
         * @classdesc Represents an ExitLiveRoomEvent.
         * @implements IExitLiveRoomEvent
         * @constructor
         * @param {live.IExitLiveRoomEvent=} [properties] Properties to set
         */
        function ExitLiveRoomEvent(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ExitLiveRoomEvent success.
         * @member {boolean} success
         * @memberof live.ExitLiveRoomEvent
         * @instance
         */
        ExitLiveRoomEvent.prototype.success = false;

        /**
         * ExitLiveRoomEvent errorMessage.
         * @member {string} errorMessage
         * @memberof live.ExitLiveRoomEvent
         * @instance
         */
        ExitLiveRoomEvent.prototype.errorMessage = "";

        /**
         * ExitLiveRoomEvent roomId.
         * @member {string} roomId
         * @memberof live.ExitLiveRoomEvent
         * @instance
         */
        ExitLiveRoomEvent.prototype.roomId = "";

        /**
         * Creates a new ExitLiveRoomEvent instance using the specified properties.
         * @function create
         * @memberof live.ExitLiveRoomEvent
         * @static
         * @param {live.IExitLiveRoomEvent=} [properties] Properties to set
         * @returns {live.ExitLiveRoomEvent} ExitLiveRoomEvent instance
         */
        ExitLiveRoomEvent.create = function create(properties) {
            return new ExitLiveRoomEvent(properties);
        };

        /**
         * Encodes the specified ExitLiveRoomEvent message. Does not implicitly {@link live.ExitLiveRoomEvent.verify|verify} messages.
         * @function encode
         * @memberof live.ExitLiveRoomEvent
         * @static
         * @param {live.IExitLiveRoomEvent} message ExitLiveRoomEvent message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ExitLiveRoomEvent.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.success != null && Object.hasOwnProperty.call(message, "success"))
                writer.uint32(/* id 1, wireType 0 =*/8).bool(message.success);
            if (message.errorMessage != null && Object.hasOwnProperty.call(message, "errorMessage"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.errorMessage);
            if (message.roomId != null && Object.hasOwnProperty.call(message, "roomId"))
                writer.uint32(/* id 5, wireType 2 =*/42).string(message.roomId);
            return writer;
        };

        /**
         * Encodes the specified ExitLiveRoomEvent message, length delimited. Does not implicitly {@link live.ExitLiveRoomEvent.verify|verify} messages.
         * @function encodeDelimited
         * @memberof live.ExitLiveRoomEvent
         * @static
         * @param {live.IExitLiveRoomEvent} message ExitLiveRoomEvent message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ExitLiveRoomEvent.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an ExitLiveRoomEvent message from the specified reader or buffer.
         * @function decode
         * @memberof live.ExitLiveRoomEvent
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {live.ExitLiveRoomEvent} ExitLiveRoomEvent
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ExitLiveRoomEvent.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.live.ExitLiveRoomEvent();
            while (reader.pos < end) {
                let tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.success = reader.bool();
                        break;
                    }
                case 3: {
                        message.errorMessage = reader.string();
                        break;
                    }
                case 5: {
                        message.roomId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an ExitLiveRoomEvent message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof live.ExitLiveRoomEvent
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {live.ExitLiveRoomEvent} ExitLiveRoomEvent
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ExitLiveRoomEvent.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an ExitLiveRoomEvent message.
         * @function verify
         * @memberof live.ExitLiveRoomEvent
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ExitLiveRoomEvent.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.success != null && message.hasOwnProperty("success"))
                if (typeof message.success !== "boolean")
                    return "success: boolean expected";
            if (message.errorMessage != null && message.hasOwnProperty("errorMessage"))
                if (!$util.isString(message.errorMessage))
                    return "errorMessage: string expected";
            if (message.roomId != null && message.hasOwnProperty("roomId"))
                if (!$util.isString(message.roomId))
                    return "roomId: string expected";
            return null;
        };

        /**
         * Creates an ExitLiveRoomEvent message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof live.ExitLiveRoomEvent
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {live.ExitLiveRoomEvent} ExitLiveRoomEvent
         */
        ExitLiveRoomEvent.fromObject = function fromObject(object) {
            if (object instanceof $root.live.ExitLiveRoomEvent)
                return object;
            let message = new $root.live.ExitLiveRoomEvent();
            if (object.success != null)
                message.success = Boolean(object.success);
            if (object.errorMessage != null)
                message.errorMessage = String(object.errorMessage);
            if (object.roomId != null)
                message.roomId = String(object.roomId);
            return message;
        };

        /**
         * Creates a plain object from an ExitLiveRoomEvent message. Also converts values to other types if specified.
         * @function toObject
         * @memberof live.ExitLiveRoomEvent
         * @static
         * @param {live.ExitLiveRoomEvent} message ExitLiveRoomEvent
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ExitLiveRoomEvent.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults) {
                object.success = false;
                object.errorMessage = "";
                object.roomId = "";
            }
            if (message.success != null && message.hasOwnProperty("success"))
                object.success = message.success;
            if (message.errorMessage != null && message.hasOwnProperty("errorMessage"))
                object.errorMessage = message.errorMessage;
            if (message.roomId != null && message.hasOwnProperty("roomId"))
                object.roomId = message.roomId;
            return object;
        };

        /**
         * Converts this ExitLiveRoomEvent to JSON.
         * @function toJSON
         * @memberof live.ExitLiveRoomEvent
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ExitLiveRoomEvent.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ExitLiveRoomEvent
         * @function getTypeUrl
         * @memberof live.ExitLiveRoomEvent
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ExitLiveRoomEvent.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/live.ExitLiveRoomEvent";
        };

        return ExitLiveRoomEvent;
    })();

    return live;
})();

export { $root as default };
