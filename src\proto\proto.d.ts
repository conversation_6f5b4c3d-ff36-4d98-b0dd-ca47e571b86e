import * as $protobuf from "protobufjs";
import Long = require("long");
/** Namespace auth. */
export namespace auth {

    /** Properties of a LoginRequest. */
    interface ILoginRequest {

        /** LoginRequest token */
        token?: (string|null);

        /** LoginRequest devicePlatform */
        devicePlatform?: (common.DevicePlatform|null);
    }

    /** Represents a LoginRequest. */
    class LoginRequest implements ILoginRequest {

        /**
         * Constructs a new LoginRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: auth.ILoginRequest);

        /** LoginRequest token. */
        public token: string;

        /** LoginRequest devicePlatform. */
        public devicePlatform: common.DevicePlatform;

        /**
         * Creates a new LoginRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LoginRequest instance
         */
        public static create(properties?: auth.ILoginRequest): auth.LoginRequest;

        /**
         * Encodes the specified LoginRequest message. Does not implicitly {@link auth.LoginRequest.verify|verify} messages.
         * @param message LoginRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: auth.ILoginRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LoginRequest message, length delimited. Does not implicitly {@link auth.LoginRequest.verify|verify} messages.
         * @param message LoginRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: auth.ILoginRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LoginRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LoginRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): auth.LoginRequest;

        /**
         * Decodes a LoginRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LoginRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): auth.LoginRequest;

        /**
         * Verifies a LoginRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LoginRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LoginRequest
         */
        public static fromObject(object: { [k: string]: any }): auth.LoginRequest;

        /**
         * Creates a plain object from a LoginRequest message. Also converts values to other types if specified.
         * @param message LoginRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: auth.LoginRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LoginRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for LoginRequest
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a LoginResponse. */
    interface ILoginResponse {

        /** LoginResponse success */
        success?: (boolean|null);

        /** LoginResponse errorMessage */
        errorMessage?: (string|null);

        /** LoginResponse user */
        user?: (common.IUser|null);
    }

    /** Represents a LoginResponse. */
    class LoginResponse implements ILoginResponse {

        /**
         * Constructs a new LoginResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: auth.ILoginResponse);

        /** LoginResponse success. */
        public success: boolean;

        /** LoginResponse errorMessage. */
        public errorMessage: string;

        /** LoginResponse user. */
        public user?: (common.IUser|null);

        /**
         * Creates a new LoginResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LoginResponse instance
         */
        public static create(properties?: auth.ILoginResponse): auth.LoginResponse;

        /**
         * Encodes the specified LoginResponse message. Does not implicitly {@link auth.LoginResponse.verify|verify} messages.
         * @param message LoginResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: auth.ILoginResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LoginResponse message, length delimited. Does not implicitly {@link auth.LoginResponse.verify|verify} messages.
         * @param message LoginResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: auth.ILoginResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LoginResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LoginResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): auth.LoginResponse;

        /**
         * Decodes a LoginResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LoginResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): auth.LoginResponse;

        /**
         * Verifies a LoginResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LoginResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LoginResponse
         */
        public static fromObject(object: { [k: string]: any }): auth.LoginResponse;

        /**
         * Creates a plain object from a LoginResponse message. Also converts values to other types if specified.
         * @param message LoginResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: auth.LoginResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LoginResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for LoginResponse
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a LogoutRequest. */
    interface ILogoutRequest {

        /** LogoutRequest devicePlatform */
        devicePlatform?: (common.DevicePlatform|null);
    }

    /** Represents a LogoutRequest. */
    class LogoutRequest implements ILogoutRequest {

        /**
         * Constructs a new LogoutRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: auth.ILogoutRequest);

        /** LogoutRequest devicePlatform. */
        public devicePlatform: common.DevicePlatform;

        /**
         * Creates a new LogoutRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LogoutRequest instance
         */
        public static create(properties?: auth.ILogoutRequest): auth.LogoutRequest;

        /**
         * Encodes the specified LogoutRequest message. Does not implicitly {@link auth.LogoutRequest.verify|verify} messages.
         * @param message LogoutRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: auth.ILogoutRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LogoutRequest message, length delimited. Does not implicitly {@link auth.LogoutRequest.verify|verify} messages.
         * @param message LogoutRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: auth.ILogoutRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LogoutRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LogoutRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): auth.LogoutRequest;

        /**
         * Decodes a LogoutRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LogoutRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): auth.LogoutRequest;

        /**
         * Verifies a LogoutRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LogoutRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LogoutRequest
         */
        public static fromObject(object: { [k: string]: any }): auth.LogoutRequest;

        /**
         * Creates a plain object from a LogoutRequest message. Also converts values to other types if specified.
         * @param message LogoutRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: auth.LogoutRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LogoutRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for LogoutRequest
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a LogoutResponse. */
    interface ILogoutResponse {

        /** LogoutResponse logoutReason */
        logoutReason?: (string|null);
    }

    /** Represents a LogoutResponse. */
    class LogoutResponse implements ILogoutResponse {

        /**
         * Constructs a new LogoutResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: auth.ILogoutResponse);

        /** LogoutResponse logoutReason. */
        public logoutReason: string;

        /**
         * Creates a new LogoutResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LogoutResponse instance
         */
        public static create(properties?: auth.ILogoutResponse): auth.LogoutResponse;

        /**
         * Encodes the specified LogoutResponse message. Does not implicitly {@link auth.LogoutResponse.verify|verify} messages.
         * @param message LogoutResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: auth.ILogoutResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LogoutResponse message, length delimited. Does not implicitly {@link auth.LogoutResponse.verify|verify} messages.
         * @param message LogoutResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: auth.ILogoutResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LogoutResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LogoutResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): auth.LogoutResponse;

        /**
         * Decodes a LogoutResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LogoutResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): auth.LogoutResponse;

        /**
         * Verifies a LogoutResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LogoutResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LogoutResponse
         */
        public static fromObject(object: { [k: string]: any }): auth.LogoutResponse;

        /**
         * Creates a plain object from a LogoutResponse message. Also converts values to other types if specified.
         * @param message LogoutResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: auth.LogoutResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LogoutResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for LogoutResponse
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }
}

/** Namespace common. */
export namespace common {

    /** DevicePlatform enum. */
    enum DevicePlatform {
        MOBILE = 0,
        WEB = 1
    }

    /** Properties of a User. */
    interface IUser {

        /** User userId */
        userId?: (string|null);

        /** User username */
        username?: (string|null);

        /** User avatar */
        avatar?: (string|null);

        /** User level */
        level?: (string|null);

        /** User tag */
        tag?: (string|null);
    }

    /** Represents a User. */
    class User implements IUser {

        /**
         * Constructs a new User.
         * @param [properties] Properties to set
         */
        constructor(properties?: common.IUser);

        /** User userId. */
        public userId: string;

        /** User username. */
        public username: string;

        /** User avatar. */
        public avatar: string;

        /** User level. */
        public level: string;

        /** User tag. */
        public tag: string;

        /**
         * Creates a new User instance using the specified properties.
         * @param [properties] Properties to set
         * @returns User instance
         */
        public static create(properties?: common.IUser): common.User;

        /**
         * Encodes the specified User message. Does not implicitly {@link common.User.verify|verify} messages.
         * @param message User message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: common.IUser, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified User message, length delimited. Does not implicitly {@link common.User.verify|verify} messages.
         * @param message User message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: common.IUser, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a User message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns User
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): common.User;

        /**
         * Decodes a User message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns User
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): common.User;

        /**
         * Verifies a User message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a User message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns User
         */
        public static fromObject(object: { [k: string]: any }): common.User;

        /**
         * Creates a plain object from a User message. Also converts values to other types if specified.
         * @param message User
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: common.User, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this User to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for User
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }
}

/** Namespace chat. */
export namespace chat {

    /** ChatMessageType enum. */
    enum ChatMessageType {
        TEXT = 0,
        AUDIO = 1,
        IMAGE = 2,
        VIDEO = 3,
        FILE = 4,
        GIFT = 5,
        ENTER = 6
    }

    /** ReceiverType enum. */
    enum ReceiverType {
        SINGLE = 0,
        GROUP = 1,
        CHAT_ROOM = 2,
        SUPER_GROUP = 3,
        LIVE_ROOM = 4
    }

    /** Properties of a ChatMessage. */
    interface IChatMessage {

        /** ChatMessage chatMessageType */
        chatMessageType?: (chat.ChatMessageType|null);

        /** ChatMessage senderId */
        senderId?: (string|null);

        /** ChatMessage receiverId */
        receiverId?: (string|null);

        /** ChatMessage content */
        content?: (string|null);

        /** ChatMessage sequence */
        sequence?: (number|Long|null);

        /** ChatMessage receiverType */
        receiverType?: (chat.ReceiverType|null);

        /** ChatMessage extra */
        extra?: (string|null);
    }

    /** Represents a ChatMessage. */
    class ChatMessage implements IChatMessage {

        /**
         * Constructs a new ChatMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: chat.IChatMessage);

        /** ChatMessage chatMessageType. */
        public chatMessageType: chat.ChatMessageType;

        /** ChatMessage senderId. */
        public senderId: string;

        /** ChatMessage receiverId. */
        public receiverId: string;

        /** ChatMessage content. */
        public content: string;

        /** ChatMessage sequence. */
        public sequence: (number|Long);

        /** ChatMessage receiverType. */
        public receiverType: chat.ReceiverType;

        /** ChatMessage extra. */
        public extra: string;

        /**
         * Creates a new ChatMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ChatMessage instance
         */
        public static create(properties?: chat.IChatMessage): chat.ChatMessage;

        /**
         * Encodes the specified ChatMessage message. Does not implicitly {@link chat.ChatMessage.verify|verify} messages.
         * @param message ChatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: chat.IChatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ChatMessage message, length delimited. Does not implicitly {@link chat.ChatMessage.verify|verify} messages.
         * @param message ChatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: chat.IChatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ChatMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): chat.ChatMessage;

        /**
         * Decodes a ChatMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): chat.ChatMessage;

        /**
         * Verifies a ChatMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ChatMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ChatMessage
         */
        public static fromObject(object: { [k: string]: any }): chat.ChatMessage;

        /**
         * Creates a plain object from a ChatMessage message. Also converts values to other types if specified.
         * @param message ChatMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: chat.ChatMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ChatMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ChatMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an Attachment. */
    interface IAttachment {

        /** Attachment url */
        url?: (string|null);
    }

    /** Represents an Attachment. */
    class Attachment implements IAttachment {

        /**
         * Constructs a new Attachment.
         * @param [properties] Properties to set
         */
        constructor(properties?: chat.IAttachment);

        /** Attachment url. */
        public url: string;

        /**
         * Creates a new Attachment instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Attachment instance
         */
        public static create(properties?: chat.IAttachment): chat.Attachment;

        /**
         * Encodes the specified Attachment message. Does not implicitly {@link chat.Attachment.verify|verify} messages.
         * @param message Attachment message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: chat.IAttachment, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Attachment message, length delimited. Does not implicitly {@link chat.Attachment.verify|verify} messages.
         * @param message Attachment message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: chat.IAttachment, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an Attachment message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Attachment
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): chat.Attachment;

        /**
         * Decodes an Attachment message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Attachment
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): chat.Attachment;

        /**
         * Verifies an Attachment message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an Attachment message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Attachment
         */
        public static fromObject(object: { [k: string]: any }): chat.Attachment;

        /**
         * Creates a plain object from an Attachment message. Also converts values to other types if specified.
         * @param message Attachment
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: chat.Attachment, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Attachment to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for Attachment
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a MessageAck. */
    interface IMessageAck {

        /** MessageAck messageId */
        messageId?: (string|null);

        /** MessageAck success */
        success?: (boolean|null);

        /** MessageAck sequence */
        sequence?: (number|Long|null);
    }

    /** Represents a MessageAck. */
    class MessageAck implements IMessageAck {

        /**
         * Constructs a new MessageAck.
         * @param [properties] Properties to set
         */
        constructor(properties?: chat.IMessageAck);

        /** MessageAck messageId. */
        public messageId: string;

        /** MessageAck success. */
        public success: boolean;

        /** MessageAck sequence. */
        public sequence: (number|Long);

        /**
         * Creates a new MessageAck instance using the specified properties.
         * @param [properties] Properties to set
         * @returns MessageAck instance
         */
        public static create(properties?: chat.IMessageAck): chat.MessageAck;

        /**
         * Encodes the specified MessageAck message. Does not implicitly {@link chat.MessageAck.verify|verify} messages.
         * @param message MessageAck message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: chat.IMessageAck, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified MessageAck message, length delimited. Does not implicitly {@link chat.MessageAck.verify|verify} messages.
         * @param message MessageAck message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: chat.IMessageAck, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a MessageAck message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns MessageAck
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): chat.MessageAck;

        /**
         * Decodes a MessageAck message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns MessageAck
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): chat.MessageAck;

        /**
         * Verifies a MessageAck message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a MessageAck message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns MessageAck
         */
        public static fromObject(object: { [k: string]: any }): chat.MessageAck;

        /**
         * Creates a plain object from a MessageAck message. Also converts values to other types if specified.
         * @param message MessageAck
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: chat.MessageAck, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this MessageAck to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for MessageAck
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a HistoryMessages. */
    interface IHistoryMessages {

        /** HistoryMessages histories */
        histories?: (chat.IChatMessage[]|null);
    }

    /** Represents a HistoryMessages. */
    class HistoryMessages implements IHistoryMessages {

        /**
         * Constructs a new HistoryMessages.
         * @param [properties] Properties to set
         */
        constructor(properties?: chat.IHistoryMessages);

        /** HistoryMessages histories. */
        public histories: chat.IChatMessage[];

        /**
         * Creates a new HistoryMessages instance using the specified properties.
         * @param [properties] Properties to set
         * @returns HistoryMessages instance
         */
        public static create(properties?: chat.IHistoryMessages): chat.HistoryMessages;

        /**
         * Encodes the specified HistoryMessages message. Does not implicitly {@link chat.HistoryMessages.verify|verify} messages.
         * @param message HistoryMessages message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: chat.IHistoryMessages, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified HistoryMessages message, length delimited. Does not implicitly {@link chat.HistoryMessages.verify|verify} messages.
         * @param message HistoryMessages message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: chat.IHistoryMessages, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a HistoryMessages message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns HistoryMessages
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): chat.HistoryMessages;

        /**
         * Decodes a HistoryMessages message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns HistoryMessages
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): chat.HistoryMessages;

        /**
         * Verifies a HistoryMessages message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a HistoryMessages message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns HistoryMessages
         */
        public static fromObject(object: { [k: string]: any }): chat.HistoryMessages;

        /**
         * Creates a plain object from a HistoryMessages message. Also converts values to other types if specified.
         * @param message HistoryMessages
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: chat.HistoryMessages, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this HistoryMessages to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for HistoryMessages
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }
}

/** Properties of a IMMessage. */
export interface IIMMessage {

    /** IMMessage header */
    header?: (IHeader|null);

    /** IMMessage loginRequest */
    loginRequest?: (auth.ILoginRequest|null);

    /** IMMessage loginResponse */
    loginResponse?: (auth.ILoginResponse|null);

    /** IMMessage logoutRequest */
    logoutRequest?: (auth.ILogoutRequest|null);

    /** IMMessage logoutResponse */
    logoutResponse?: (auth.ILogoutResponse|null);

    /** IMMessage chatMessage */
    chatMessage?: (chat.IChatMessage|null);

    /** IMMessage messageAck */
    messageAck?: (chat.IMessageAck|null);

    /** IMMessage historyMessages */
    historyMessages?: (chat.IHistoryMessages|null);

    /** IMMessage enterLiveRoomEvent */
    enterLiveRoomEvent?: (live.IEnterLiveRoomEvent|null);

    /** IMMessage exitLiveRoomEvent */
    exitLiveRoomEvent?: (live.IExitLiveRoomEvent|null);

    /** IMMessage heartbeat */
    heartbeat?: (string|null);
}

/** Represents a IMMessage. */
export class IMMessage implements IIMMessage {

    /**
     * Constructs a new IMMessage.
     * @param [properties] Properties to set
     */
    constructor(properties?: IIMMessage);

    /** IMMessage header. */
    public header?: (IHeader|null);

    /** IMMessage loginRequest. */
    public loginRequest?: (auth.ILoginRequest|null);

    /** IMMessage loginResponse. */
    public loginResponse?: (auth.ILoginResponse|null);

    /** IMMessage logoutRequest. */
    public logoutRequest?: (auth.ILogoutRequest|null);

    /** IMMessage logoutResponse. */
    public logoutResponse?: (auth.ILogoutResponse|null);

    /** IMMessage chatMessage. */
    public chatMessage?: (chat.IChatMessage|null);

    /** IMMessage messageAck. */
    public messageAck?: (chat.IMessageAck|null);

    /** IMMessage historyMessages. */
    public historyMessages?: (chat.IHistoryMessages|null);

    /** IMMessage enterLiveRoomEvent. */
    public enterLiveRoomEvent?: (live.IEnterLiveRoomEvent|null);

    /** IMMessage exitLiveRoomEvent. */
    public exitLiveRoomEvent?: (live.IExitLiveRoomEvent|null);

    /** IMMessage heartbeat. */
    public heartbeat?: (string|null);

    /** IMMessage body. */
    public body?: ("loginRequest"|"loginResponse"|"logoutRequest"|"logoutResponse"|"chatMessage"|"messageAck"|"historyMessages"|"enterLiveRoomEvent"|"exitLiveRoomEvent"|"heartbeat");

    /**
     * Creates a new IMMessage instance using the specified properties.
     * @param [properties] Properties to set
     * @returns IMMessage instance
     */
    public static create(properties?: IIMMessage): IMMessage;

    /**
     * Encodes the specified IMMessage message. Does not implicitly {@link IMMessage.verify|verify} messages.
     * @param message IMMessage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IIMMessage, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified IMMessage message, length delimited. Does not implicitly {@link IMMessage.verify|verify} messages.
     * @param message IMMessage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IIMMessage, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a IMMessage message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns IMMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): IMMessage;

    /**
     * Decodes a IMMessage message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns IMMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): IMMessage;

    /**
     * Verifies a IMMessage message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a IMMessage message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns IMMessage
     */
    public static fromObject(object: { [k: string]: any }): IMMessage;

    /**
     * Creates a plain object from a IMMessage message. Also converts values to other types if specified.
     * @param message IMMessage
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: IMMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this IMMessage to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for IMMessage
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a Header. */
export interface IHeader {

    /** Header messageId */
    messageId?: (string|null);

    /** Header timestamp */
    timestamp?: (number|Long|null);

    /** Header version */
    version?: (number|null);

    /** Header user */
    user?: (common.IUser|null);
}

/** Represents a Header. */
export class Header implements IHeader {

    /**
     * Constructs a new Header.
     * @param [properties] Properties to set
     */
    constructor(properties?: IHeader);

    /** Header messageId. */
    public messageId: string;

    /** Header timestamp. */
    public timestamp: (number|Long);

    /** Header version. */
    public version: number;

    /** Header user. */
    public user?: (common.IUser|null);

    /**
     * Creates a new Header instance using the specified properties.
     * @param [properties] Properties to set
     * @returns Header instance
     */
    public static create(properties?: IHeader): Header;

    /**
     * Encodes the specified Header message. Does not implicitly {@link Header.verify|verify} messages.
     * @param message Header message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IHeader, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified Header message, length delimited. Does not implicitly {@link Header.verify|verify} messages.
     * @param message Header message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IHeader, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a Header message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns Header
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): Header;

    /**
     * Decodes a Header message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns Header
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): Header;

    /**
     * Verifies a Header message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a Header message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns Header
     */
    public static fromObject(object: { [k: string]: any }): Header;

    /**
     * Creates a plain object from a Header message. Also converts values to other types if specified.
     * @param message Header
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: Header, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this Header to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for Header
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Namespace live. */
export namespace live {

    /** Properties of an EnterLiveRoomEvent. */
    interface IEnterLiveRoomEvent {

        /** EnterLiveRoomEvent roomId */
        roomId?: (string|null);

        /** EnterLiveRoomEvent userType */
        userType?: (live.UserType|null);
    }

    /** Represents an EnterLiveRoomEvent. */
    class EnterLiveRoomEvent implements IEnterLiveRoomEvent {

        /**
         * Constructs a new EnterLiveRoomEvent.
         * @param [properties] Properties to set
         */
        constructor(properties?: live.IEnterLiveRoomEvent);

        /** EnterLiveRoomEvent roomId. */
        public roomId: string;

        /** EnterLiveRoomEvent userType. */
        public userType: live.UserType;

        /**
         * Creates a new EnterLiveRoomEvent instance using the specified properties.
         * @param [properties] Properties to set
         * @returns EnterLiveRoomEvent instance
         */
        public static create(properties?: live.IEnterLiveRoomEvent): live.EnterLiveRoomEvent;

        /**
         * Encodes the specified EnterLiveRoomEvent message. Does not implicitly {@link live.EnterLiveRoomEvent.verify|verify} messages.
         * @param message EnterLiveRoomEvent message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: live.IEnterLiveRoomEvent, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified EnterLiveRoomEvent message, length delimited. Does not implicitly {@link live.EnterLiveRoomEvent.verify|verify} messages.
         * @param message EnterLiveRoomEvent message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: live.IEnterLiveRoomEvent, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an EnterLiveRoomEvent message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns EnterLiveRoomEvent
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): live.EnterLiveRoomEvent;

        /**
         * Decodes an EnterLiveRoomEvent message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns EnterLiveRoomEvent
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): live.EnterLiveRoomEvent;

        /**
         * Verifies an EnterLiveRoomEvent message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an EnterLiveRoomEvent message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns EnterLiveRoomEvent
         */
        public static fromObject(object: { [k: string]: any }): live.EnterLiveRoomEvent;

        /**
         * Creates a plain object from an EnterLiveRoomEvent message. Also converts values to other types if specified.
         * @param message EnterLiveRoomEvent
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: live.EnterLiveRoomEvent, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this EnterLiveRoomEvent to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for EnterLiveRoomEvent
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** UserType enum. */
    enum UserType {
        ANONYMOUS = 0,
        SIGNED = 1
    }

    /** Properties of an ExitLiveRoomEvent. */
    interface IExitLiveRoomEvent {

        /** ExitLiveRoomEvent success */
        success?: (boolean|null);

        /** ExitLiveRoomEvent errorMessage */
        errorMessage?: (string|null);

        /** ExitLiveRoomEvent roomId */
        roomId?: (string|null);
    }

    /** Represents an ExitLiveRoomEvent. */
    class ExitLiveRoomEvent implements IExitLiveRoomEvent {

        /**
         * Constructs a new ExitLiveRoomEvent.
         * @param [properties] Properties to set
         */
        constructor(properties?: live.IExitLiveRoomEvent);

        /** ExitLiveRoomEvent success. */
        public success: boolean;

        /** ExitLiveRoomEvent errorMessage. */
        public errorMessage: string;

        /** ExitLiveRoomEvent roomId. */
        public roomId: string;

        /**
         * Creates a new ExitLiveRoomEvent instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ExitLiveRoomEvent instance
         */
        public static create(properties?: live.IExitLiveRoomEvent): live.ExitLiveRoomEvent;

        /**
         * Encodes the specified ExitLiveRoomEvent message. Does not implicitly {@link live.ExitLiveRoomEvent.verify|verify} messages.
         * @param message ExitLiveRoomEvent message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: live.IExitLiveRoomEvent, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ExitLiveRoomEvent message, length delimited. Does not implicitly {@link live.ExitLiveRoomEvent.verify|verify} messages.
         * @param message ExitLiveRoomEvent message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: live.IExitLiveRoomEvent, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an ExitLiveRoomEvent message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ExitLiveRoomEvent
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): live.ExitLiveRoomEvent;

        /**
         * Decodes an ExitLiveRoomEvent message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ExitLiveRoomEvent
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): live.ExitLiveRoomEvent;

        /**
         * Verifies an ExitLiveRoomEvent message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an ExitLiveRoomEvent message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ExitLiveRoomEvent
         */
        public static fromObject(object: { [k: string]: any }): live.ExitLiveRoomEvent;

        /**
         * Creates a plain object from an ExitLiveRoomEvent message. Also converts values to other types if specified.
         * @param message ExitLiveRoomEvent
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: live.ExitLiveRoomEvent, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ExitLiveRoomEvent to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ExitLiveRoomEvent
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }
}
