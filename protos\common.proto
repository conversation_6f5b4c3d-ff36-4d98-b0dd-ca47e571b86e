syntax = "proto3";

package common;

option java_package = "com.letu.solutions.model.netty.v2.protobuf.common";
option java_outer_classname = "Common";


// 设备平台
enum DevicePlatform {
  MOBILE = 0;  //手机端
  WEB = 1;     //客户端
}

// 用户结构
message User {
  string user_id = 1;  //用户di
  string username = 2; //用户昵称
  string avatar = 3;   //用户头像
  string level = 4;    //用户等级
  string tag = 5 ;     //用户标签
}
