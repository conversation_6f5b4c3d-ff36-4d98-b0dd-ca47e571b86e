<template>
  <div class="chat-warp">
    <div class="chat-list">
      <div class="chat-item" v-for="(chat, index) in receivedMessages" :key="index">
        <div class="enter-box" v-if="chat.type === 'ENTER'">
          <span class="label">Hi</span><span class="user-name">{{ chat.user.userName }}</span><span
            class="tip">进入直播间</span>
        </div>
        <div class="text-box" v-if="chat.type === 'TEXT'">
          <div class="level">{{ chat.user.level ? chat.user.level : 1 }}</div>
          <div class="user-name">{{ chat.user.userName }}：</div>
          <div class="content">{{ chat.content }}</div>
        </div>
      </div>
    </div>
    <div class="chat-bottom">
      <div class="chat-input-warp">
        <!-- <n-input v-model:value="sendContent" class="border-none" type="textarea" :autosize="{ minRows: 2, maxRows: 2, }"
          placeholder="请输入消息..." /> -->
        <textarea v-model="sendContent" :maxLength="40" class="chat-textarea" placeholder="请输入消息..."></textarea>
        <button class="send-btn" @click="sendChatMessage">发送</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import * as live from '@/proto/proto.js'
import { useUserStore } from '@/stores/user'
import { findByRoomIdApi } from '@/api/match'

const props = defineProps({
  userId: {
    type: Number,
    default: 8497
  },
})
//查询聊天室近10条消息
const findByRoomId = async () => {
  let res = await findByRoomIdApi({ roomId: props.userId })
  if (res.data.length > 0) {
    res.data.forEach((item) => {
      let b =
        receivedMessages.value.push()
    })

  }
}
findByRoomId()
const userStore = useUserStore()
const sendContent = ref('')
const websocket = ref<WebSocket | null>(null)
// 连接状态
const connectionStatus = ref('disconnected')
// 连接状态消息
const connectionStatusMessage = ref('未连接')
// 心跳定时器
const heartbeatTimer = ref<NodeJS.Timeout | null>(null)
// 心跳间隔时间
const heartbeatInterval = ref(15000)
// 接收到的消息
interface ReceivedMessage {
  content: string;
  type: string
  user: {
    id: string,
    userName: string,
    avatar?: string,
    level?: string,
    tag?: string
  }
}
const receivedMessages = ref<ReceivedMessage[]>([])
const connectWebSocket = () => {
  const url = 'ws://192.168.77.217:10710/';
  // const url = 'ws://192.168.77.102:31170/'
  // 更新连接状态为连接中
  connectionStatus.value = 'connecting';
  connectionStatusMessage.value = '正在连接...';
  websocket.value = new WebSocket(url)

  websocket.value.onopen = () => {
    connectionStatus.value = 'connected'
    connectionStatusMessage.value = '已连接'
    // 启动心跳检测
    startHeartbeat()
    //是否登录
    if (userStore.isLogin) {
      sendLoginReq()
    } else {
      //游客进入
      touristEnterLiveRoom()
    }
  }

  websocket.value.onmessage = (event) => {
    try {
      // 更新连接状态
      connectionStatus.value = 'connected';
      connectionStatusMessage.value = '已连接';

      // Handle binary data
      if (event.data instanceof Blob) {
        const reader = new FileReader();
        reader.onload = () => {
          const buffer = new Uint8Array(reader.result as ArrayBuffer);
          handleReceivedMessage(buffer);
        };
        reader.readAsArrayBuffer(event.data);
      } else {
        console.log('Received non-binary message:', event.data);
      }
    } catch (error) {
      console.error('Error processing received message:', error);
    }
  }

  websocket.value.onerror = (error) => {
    console.error('WebSocket error:', error)
  }

  websocket.value.onclose = () => {
    connectionStatus.value = 'disconnected'
    connectionStatusMessage.value = '已断开'
    console.log('链接已断开')
    // 停止心跳检测
    stopHeartbeat()
  }
}
//处理接收到的消息
const handleReceivedMessage = (buffer) => {
  try {
    // 使用正确的路径解码消息
    const IMMessage = live.IMMessage;

    const decodedMessage = IMMessage.decode(buffer);
    // 使用toObject转换为友好的格式显示
    const jsonMessage = IMMessage.toObject(decodedMessage, {
      enums: String,  // 将枚举值显示为字符串
      longs: String,  // 将长整型显示为字符串
      bytes: String,  // 将字节显示为字符串
      defaults: true, // 显示默认值
      arrays: true,   // 保留空数组
      objects: true,  // 保留空对象
      oneofs: true    // 保留oneof字段信息
    });
    // 获取消息类型（从oneof字段中获取）
    let messageType = 'unknown';
    if (jsonMessage.body === 'loginResponse') {
      jsonMessage.loginResponse.success ? console.log('登录成功') : console.log('登录失败')
      //进入直播间
      enterLiveRoom()
    }
    if (jsonMessage.body === 'chatMessage') {
      console.log(jsonMessage, "jsonMessage")
      messageType = Object.keys(jsonMessage.body)[0];
      // const messageId = jsonMessage.header?.messageId || `msg_${Date.now()}`;
      const messageRecord = {
        type: jsonMessage.chatMessage.chatMessageType,
        content: jsonMessage.chatMessage.content,
        user: {
          id: jsonMessage.header.user.userId,
          userName: jsonMessage.header.user.username,
          avatar: jsonMessage.header.user.avatar,
          level: jsonMessage.header.user.avatar,
          tag: jsonMessage.header.user.tag
        }
      };
      receivedMessages.value.push(messageRecord);
    }

    // 添加到消息列表
    // receivedMessages.value.unshift(messageRecord);
    // 保持最多20条消息
    // if (receivedMessages.value.length > 20) {
    //   receivedMessages.value = receivedMessages.value.slice(0, 20);
    // }
  } catch (error) {
    console.error('解码消息时出错:', error);
  }
}
// 发送心跳消息
const sendHeartbeat = () => {
  if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
    try {
      const IMMessage = live.IMMessage;
      const Header = live.Header;

      // 创建消息头
      const header = Header.create({
        messageId: 'heartbeat_' + Date.now(),
        timestamp: Date.now(),
        version: 1
      });

      // 创建IM消息
      const imMessage = IMMessage.create({
        header: header,
        heartbeat: 'ping'
      });

      // 编码消息
      const buffer = IMMessage.encode(imMessage).finish();

      // 发送心跳消息
      websocket.value.send(buffer);
      // console.log('已发送心跳消息');
    } catch (error) {
      console.error('发送心跳消息失败:', error);
    }
  } else {
    console.warn('WebSocket未连接，无法发送心跳');
    // WebSocket已断开，尝试重新连接
    if (connectionStatus.value !== 'connecting') {
      console.log('检测到WebSocket已断开，尝试重新连接');
      reconnectWebSocket();
    }
  }
}
// 尝试重新连接WebSocket
const reconnectWebSocket = () => {
  // 确保先停止现有的心跳
  stopHeartbeat();
  connectWebSocket();
}
//停止心跳
const disconnectWebSocket = () => {
  // 停止心跳
  stopHeartbeat();
  if (websocket.value) {
    websocket.value.close();
    websocket.value = null;
    connectionStatus.value = 'disconnected';
    connectionStatusMessage.value = '未连接';
  }
}
// 开始心跳检测
const startHeartbeat = () => {
  // 先清除可能存在的定时器
  stopHeartbeat();

  // 创建新的定时器，每15秒发送一次心跳
  heartbeatTimer.value = setInterval(() => {
    sendHeartbeat();
  }, heartbeatInterval.value);

  console.log('心跳检测已启动，间隔：', heartbeatInterval, '毫秒');
}
// 停止心跳检测
const stopHeartbeat = () => {
  if (heartbeatTimer.value) {
    clearInterval(heartbeatTimer.value);
    heartbeatTimer.value = null;
    console.log('心跳检测已停止');
  }
}
//发送登录请求
const sendLoginReq = () => {
  // 正确获取IMMessage构造函数
  const IMMessage = live.IMMessage;
  // 创建消息头
  const Header = live.Header;
  const header = Header.create({
    messageId: generateMessageId(),
    timestamp: Date.now(),
    version: 1
  });

  // Create the IMMessage with the header
  const imMessage = IMMessage.create({
    header: header,
    loginRequest: {
      token: userStore.userToken,
      devicePlatform: live.common.DevicePlatform.WEB
    }
  });
  // Encode the message
  const buffer = IMMessage.encode(imMessage).finish();

  // Send the message if websocket is connected
  if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
    websocket.value.send(buffer);
    console.log('发送登录请求');
  }
}
//登录用户进入房间
const enterLiveRoom = () => {
  // 正确获取IMMessage构造函数
  const IMMessage = live.IMMessage;
  // 创建消息头
  const Header = live.Header;
  const header = Header.create({
    messageId: generateMessageId(),
    timestamp: Date.now(),
    version: 1
  });

  const imMessage = IMMessage.create({
    header: header,
    enterLiveRoomEvent: {
      roomId: props.userId.toString(),
      userType: live.live.UserType.SIGNED
    }
  });
  // Encode the message
  const buffer = IMMessage.encode(imMessage).finish();

  // Send the message if websocket is connected
  if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
    websocket.value.send(buffer);
    console.log('发送进入直播间请求');
  }
}
//匿名用户进入房间
const touristEnterLiveRoom = () => {
  // 正确获取IMMessage构造函数
  const IMMessage = live.IMMessage;
  // 创建消息头
  const Header = live.Header;
  const header = Header.create({
    messageId: generateMessageId(),
    timestamp: Date.now(),
    version: 1
  });

  const imMessage = IMMessage.create({
    header: header,
    enterLiveRoomEvent: {
      roomId: props.userId.toString(),
      userType: live.live.UserType.ANONYMOUS
    }
  });
  // Encode the message
  const buffer = IMMessage.encode(imMessage).finish();

  // Send the message if websocket is connected
  if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
    websocket.value.send(buffer);
    console.log('游客进入直播间请求');
  }
}
//监听登录
watch(() => userStore.isLogin, (newVal) => {
  if (newVal) {
    sendLoginReq()
  }
})

//生成消息ID
const generateMessageId = () => {
  return 'msg_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
}
//发送消息
const sendChatMessage = () => {
  if (!sendContent.value) {
    console.error('消息不能为空');
    return;
  }
  try {
    // 正确获取IMMessage构造函数
    const IMMessage = live.IMMessage;
    // 创建消息头
    const Header = live.Header;
    const header = Header.create({
      messageId: generateMessageId(),
      timestamp: Date.now(),
      version: 1
    });

    // Create the IMMessage with the header
    const imMessage = IMMessage.create({
      header: header,
      chatMessage: {
        chatMessageType: live.chat.ChatMessageType.TEXT,
        senderId: userStore.userInfo.id.toString(),
        receiverId: props.userId.toString(),
        content: sendContent.value,
        sequence: Date.now(),
        receiverType: live.chat.ReceiverType.LIVE_ROOM,
        extra: ''
      }
    });
    // Encode the message
    const buffer = IMMessage.encode(imMessage).finish();

    // Send the message if websocket is connected
    if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
      websocket.value.send(buffer);
      //情况消息
      sendContent.value = '';
      console.log('Message sent:', imMessage);
    } else {
      console.error('WebSocket is not connected');
    }
  } catch (error) {
    console.error('Error sending message:', error);
  }
}
onMounted(() => {
  connectWebSocket()
})

onBeforeUnmount(() => {
  // 确保在组件卸载时断开WebSocket连接
  disconnectWebSocket();
  // 确保心跳定时器被清除
  stopHeartbeat();
  connectionStatus.value = 'disconnected';
  connectionStatusMessage.value = '未连接';
})
</script>
<style lang="scss" scoped>
.n-input {
  border: none !important;
}

.chat-warp {
  height: 100%;
  display: flex;
  flex-direction: column;

  .chat-list {
    flex: 1;
    padding: 10px;

    .chat-item {
      margin-bottom: 4px;

      .enter-box {
        display: flex;
        align-items: center;
        font-size: 14px;

        .label {
          color: #333333;
          background-color: #FA6D26;
          line-height: 1;
          font-size: 12px;
          padding: 2px;
        }

        .user-name {
          color: #3177FD;
        }

        .tip {
          color: #9E9E9E;
        }
      }

      .text-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        font-size: 14px;

        .level {
          color: #fff;
          background-color: #00C800;
          line-height: 1;
          padding: 2px 4px;
          margin-right: 4px;
          font-size: 12px;
          border-radius: 2px;
        }

        .user-name {
          color: #3177FD;
          margin-right: 4px;
        }

        .content {
          color: #333333;
          display: inline-block;
        }
      }
    }
  }

  .chat-bottom {
    padding: 10px;

    .chat-input-warp {
      display: flex;
      align-items: center;

      .chat-textarea {
        padding: 10px;
        box-sizing: border-box;
        height: 50px;
        font-size: 12px;
        color: #333;
        border: 1px solid #D9D9D9;
        border-radius: 0;
        resize: none;
        flex: 1;
        outline: none;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }

      .send-btn {
        width: 60px;
        height: 50px;
        background: #D9D9D9;
        color: #818181;
        border: none;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: none;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
  }
}
</style>